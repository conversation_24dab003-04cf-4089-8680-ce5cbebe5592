/**
 * 岗位管理API
 * 基于RuoYi接口格式，支持完整的岗位CRUD操作
 */

import { http } from '@/utils/http'
import type { Post, PostQueryParams } from '@/types/system/post'
import type { RuoyiResponse } from '@/types/http'

export class PostApi {
  /**
   * 查询岗位列表
   */
  static getPostList(params?: PostQueryParams): Promise<RuoyiResponse<Post>> {
    return http.get('/system/post/list', params)
  }

  /**
   * 查询岗位详细信息
   */
  static getPostDetail(postId: number): Promise<RuoyiResponse<Post>> {
    return http.get(`/system/post/${postId}`)
  }

  /**
   * 新增岗位
   */
  static addPost(data: Post): Promise<RuoyiResponse<any>> {
    return http.post('/system/post', data)
  }

  /**
   * 修改岗位
   */
  static updatePost(data: Post): Promise<RuoyiResponse<any>> {
    return http.put('/system/post', data)
  }

  /**
   * 删除岗位
   */
  static deletePost(postIds: number | number[]): Promise<RuoyiResponse<any>> {
    return http.delete(`/system/post/${postIds}`)
  }

  /**
   * 导出岗位列表
   */
  static exportPost(params?: PostQueryParams): Promise<any> {
    return http.download('/system/post/export', params)
  }

  /**
   * 获取岗位选项列表（用于下拉选择）
   */
  static getPostOptions(): Promise<RuoyiResponse<Post[]>> {
    return http.get('/system/post/optionselect')
  }
}

// 导出默认实例（兼容旧的调用方式）
export const {
  getPostList,
  getPostDetail,
  addPost,
  updatePost,
  deletePost,
  exportPost,
  getPostOptions
} = PostApi

// 兼容RuoYi原有的函数名
export const listPost = getPostList
export const getPost = getPostDetail
export const delPost = deletePost
