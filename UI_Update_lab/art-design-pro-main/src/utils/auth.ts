import { useUserStore } from '@/store/modules/user'
import Cookies from 'js-cookie'

/**
 * Token管理工具
 * 保持与RuoYi一致的token管理方式（使用Cookie存储）
 */

const TokenKey = 'Admin-Token'

/**
 * 获取token
 */
export function getToken(): string | null {
  return Cookies.get(TokenKey) || null
}

/**
 * 设置token
 */
export function setToken(token: string): void {
  Cookies.set(TokenKey, token)
}

/**
 * 移除token
 */
export function removeToken(): void {
  Cookies.remove(TokenKey)
}

/**
 * 权限验证工具函数
 * 直接支持RuoYi权限标识格式
 */

/**
 * 验证用户是否具备某权限
 * @param permission 权限标识，如 'system:user:add'
 * @returns 是否有权限
 */
export function hasPermi(permission: string): boolean {
  const userStore = useUserStore()
  const permissions = userStore.permissions || []

  if (!permission || permission.length === 0) {
    return false
  }

  const all_permission = '*:*:*'
  return permissions.some((v: string) => {
    return all_permission === v || v === permission
  })
}

/**
 * 验证用户是否含有指定权限，只需包含其中一个
 * @param permissions 权限标识数组
 * @returns 是否有权限
 */
export function hasPermiOr(permissions: string[]): boolean {
  return permissions.some((item) => hasPermi(item))
}

/**
 * 验证用户是否含有指定权限，必须全部拥有
 * @param permissions 权限标识数组
 * @returns 是否有权限
 */
export function hasPermiAnd(permissions: string[]): boolean {
  return permissions.every((item) => hasPermi(item))
}

/**
 * 验证用户是否具备某角色
 * @param role 角色标识，如 'admin'
 * @returns 是否有角色
 */
export function hasRole(role: string): boolean {
  const userStore = useUserStore()
  const roles = userStore.roles || []

  if (!role || role.length === 0) {
    return false
  }

  const super_admin = 'admin'
  return roles.some((v: string) => {
    return super_admin === v || v === role
  })
}

/**
 * 验证用户是否含有指定角色，只需包含其中一个
 * @param roles 角色标识数组
 * @returns 是否有角色
 */
export function hasRoleOr(roles: string[]): boolean {
  return roles.some((item) => hasRole(item))
}

/**
 * 验证用户是否含有指定角色，必须全部拥有
 * @param roles 角色标识数组
 * @returns 是否有角色
 */
export function hasRoleAnd(roles: string[]): boolean {
  return roles.every((item) => hasRole(item))
}
