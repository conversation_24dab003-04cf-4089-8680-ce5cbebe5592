<template>
  <div class="user-info-display-test">
    <h2>用户信息显示测试</h2>

    <div class="test-section">
      <h3>当前用户状态</h3>
      <p>登录状态: {{ userStore.isLogin ? '已登录' : '未登录' }}</p>
      <p>Token: {{ userStore.token ? '已设置' : '未设置' }}</p>
    </div>

    <div class="test-section">
      <h3>用户基本信息</h3>
      <div class="user-card">
        <div class="avatar-section">
          <img
            :src="userStore.userInfo.avatar || '/src/assets/img/user/avatar.webp'"
            alt="用户头像"
            class="avatar"
          />
        </div>
        <div class="info-section">
          <p><strong>用户ID:</strong> {{ userStore.userInfo.userId || '未设置' }}</p>
          <p><strong>用户名:</strong> {{ userStore.userInfo.userName || '未设置' }}</p>
          <p><strong>昵称:</strong> {{ userStore.userInfo.nickName || '未设置' }}</p>
          <p><strong>邮箱:</strong> {{ userStore.userInfo.email || '未设置' }}</p>
          <p><strong>手机号:</strong> {{ userStore.userInfo.phonenumber || '未设置' }}</p>
          <p><strong>性别:</strong> {{ getSexText(userStore.userInfo.sex) }}</p>
        </div>
      </div>
    </div>

    <div class="test-section">
      <h3>权限和角色</h3>
      <div class="permissions-section">
        <p><strong>角色列表:</strong></p>
        <div class="tags">
          <span v-for="role in userStore.roles" :key="role" class="tag role-tag">
            {{ role }}
          </span>
          <span v-if="userStore.roles.length === 0" class="no-data">无角色</span>
        </div>

        <p><strong>权限列表 (前10个):</strong></p>
        <div class="tags">
          <span
            v-for="permission in userStore.permissions.slice(0, 10)"
            :key="permission"
            class="tag permission-tag"
          >
            {{ permission }}
          </span>
          <span v-if="userStore.permissions.length === 0" class="no-data">无权限</span>
        </div>
        <p v-if="userStore.permissions.length > 10" class="more-info">
          还有 {{ userStore.permissions.length - 10 }} 个权限...
        </p>
      </div>
    </div>

    <div class="test-section">
      <h3>模拟登录测试</h3>
      <div class="action-buttons">
        <button @click="simulateLogin" class="test-btn primary">模拟登录</button>
        <button @click="simulateLogout" class="test-btn danger">模拟退出</button>
        <button @click="clearUserInfo" class="test-btn">清空用户信息</button>
      </div>
    </div>

    <div class="test-section">
      <h3>JSON数据预览</h3>
      <pre class="json-preview">{{
        JSON.stringify(
          {
            userInfo: userStore.userInfo,
            roles: userStore.roles,
            permissions: userStore.permissions.slice(0, 5)
          },
          null,
          2
        )
      }}</pre>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { useUserStore } from '@/store/modules/user'
  import type { UserInfo } from '@/types/system/auth'

  const userStore = useUserStore()

  // 获取性别文本
  const getSexText = (sex?: string) => {
    switch (sex) {
      case '0':
        return '男'
      case '1':
        return '女'
      case '2':
        return '未知'
      default:
        return '未设置'
    }
  }

  // 模拟登录
  const simulateLogin = () => {
    // 设置模拟的用户信息
    const mockUserInfo: UserInfo = {
      userId: 1,
      userName: 'admin',
      nickName: '管理员',
      email: '<EMAIL>',
      phonenumber: '15888888888',
      sex: '0',
      avatar: '/src/assets/img/user/avatar.webp'
    }

    const mockRoles = ['admin', 'common']
    const mockPermissions = [
      'system:user:add',
      'system:user:edit',
      'system:user:remove',
      'system:user:query',
      'system:role:add',
      'system:role:edit',
      'system:role:remove',
      'system:role:query',
      'system:menu:add',
      'system:menu:edit'
    ]

    // 设置token
    userStore.setTokenValue('mock-token-12345')
    // 设置用户信息
    userStore.setUserInfo(mockUserInfo)
    userStore.setRoles(mockRoles)
    userStore.setPermissions(mockPermissions)

    console.log('模拟登录成功')
  }

  // 模拟退出
  const simulateLogout = async () => {
    try {
      await userStore.logOut()
      console.log('模拟退出成功')
    } catch (error) {
      console.error('模拟退出失败:', error)
    }
  }

  // 清空用户信息
  const clearUserInfo = () => {
    userStore.setUserInfo({} as UserInfo)
    userStore.setRoles([])
    userStore.setPermissions([])
    console.log('用户信息已清空')
  }
</script>

<style scoped>
  .user-info-display-test {
    padding: 20px;
    max-width: 1000px;
    margin: 0 auto;
  }

  .test-section {
    margin-bottom: 30px;
    padding: 20px;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    background-color: #f9f9f9;
  }

  .test-section h3 {
    margin-top: 0;
    color: #333;
    border-bottom: 2px solid #1890ff;
    padding-bottom: 10px;
  }

  .user-card {
    display: flex;
    gap: 20px;
    align-items: flex-start;
  }

  .avatar-section .avatar {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    object-fit: cover;
    border: 3px solid #1890ff;
  }

  .info-section p {
    margin: 8px 0;
    color: #666;
  }

  .permissions-section .tags {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin: 10px 0;
  }

  .tag {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    color: white;
  }

  .role-tag {
    background-color: #52c41a;
  }

  .permission-tag {
    background-color: #1890ff;
  }

  .no-data {
    color: #999;
    font-style: italic;
  }

  .more-info {
    color: #666;
    font-size: 14px;
    margin-top: 10px;
  }

  .action-buttons {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
  }

  .test-btn {
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s;
  }

  .test-btn.primary {
    background-color: #1890ff;
    color: white;
  }

  .test-btn.primary:hover {
    background-color: #40a9ff;
  }

  .test-btn.danger {
    background-color: #ff4d4f;
    color: white;
  }

  .test-btn.danger:hover {
    background-color: #ff7875;
  }

  .test-btn:not(.primary):not(.danger) {
    background-color: #f0f0f0;
    color: #333;
  }

  .test-btn:not(.primary):not(.danger):hover {
    background-color: #e0e0e0;
  }

  .json-preview {
    background-color: #f5f5f5;
    padding: 15px;
    border-radius: 4px;
    overflow-x: auto;
    font-size: 12px;
    color: #333;
    border: 1px solid #d9d9d9;
  }
</style>
