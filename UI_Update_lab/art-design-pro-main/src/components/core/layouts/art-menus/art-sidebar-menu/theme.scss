@use '@styles/variables.scss' as *;
@use '@styles/mixin.scss' as *;

// 重新修改菜单样式
$menu-height: 46px; // 菜单高度
$menu-icon-size: 20px; // 菜单图标大小
$menu-font-size: 14px; // 菜单字体大小
$hover-bg-color: rgba(var(--art-gray-200-rgb), 0.8); // 鼠标移入背景色

.layout-sidebar {
  // ---------------------- Modify default style ----------------------

  // 菜单折叠样式
  .menu-left-close {
    .header {
      .logo {
        margin: 0 auto;
      }
    }
  }

  // 菜单图标
  .menu-icon {
    margin-right: 10px;
    font-size: $menu-icon-size;
  }

  // 菜单高度
  .el-sub-menu__title,
  .el-menu-item {
    height: $menu-height !important;
    margin-bottom: 4px;
    line-height: $menu-height !important;

    span {
      font-size: $menu-font-size !important;

      @include ellipsis();
    }

    &.is-active {
      .menu-icon {
        // 选中菜单图标颜色
        color: var(--main-color) !important;
      }
    }
  }

  // 右侧箭头
  .el-sub-menu__icon-arrow {
    width: 13px !important;
    font-size: 13px !important;
  }

  // 菜单折叠
  .el-menu--collapse {
    .el-sub-menu.is-active {
      .el-sub-menu__title {
        .iconfont-sys {
          // 选中菜单图标颜色
          color: var(--main-color) !important;
        }
      }
    }
  }

  // ---------------------- Design theme menu ----------------------

  .el-menu-design {
    .el-sub-menu__title,
    .el-menu-item {
      width: calc(100% - 16px);
      margin-left: 8px;
      border-radius: 6px;

      .menu-icon {
        margin-left: -4px;
      }
    }

    // 选中颜色
    .el-menu-item.is-active {
      color: var(--main-color) !important;
      background-color: var(--el-color-primary-light-9);
    }

    // 鼠标移入背景色
    .el-sub-menu__title:hover,
    .el-menu-item:not(.is-active):hover {
      background: $hover-bg-color !important;
    }

    // 右侧箭头
    .el-sub-menu__icon-arrow {
      color: var(--art-gray-600);
    }
  }

  // ---------------------- Dark theme menu ----------------------
  .el-menu-dark {
    .el-sub-menu__title,
    .el-menu-item {
      width: calc(100% - 16px);
      margin-left: 8px;
      border-radius: 6px;

      .menu-icon {
        margin-left: -4px;
      }
    }

    // 选中颜色
    .el-menu-item.is-active {
      background-color: var(--el-color-primary-light-1);

      .menu-icon {
        color: #fff !important;
      }
    }

    // 鼠标移入背景色
    .el-sub-menu__title:hover,
    .el-menu-item:not(.is-active):hover {
      background: #0f1015 !important;
    }

    // 右侧箭头
    .el-sub-menu__icon-arrow {
      color: var(--art-gray-400);
    }
  }

  // ---------------------- Light theme menu ----------------------
  .el-menu-light {
    .el-sub-menu__title,
    .el-menu-item {
      .menu-icon {
        margin-left: 4px;
      }
    }

    // 选中颜色
    .el-menu-item.is-active {
      color: var(--main-color) !important;
      background-color: var(--el-color-primary-light-9);

      &::before {
        position: absolute;
        top: 0;
        left: 0;
        width: 4px;
        height: 100%;
        content: '';
        background: var(--main-color);
      }
    }

    // 鼠标移入背景色
    .el-sub-menu__title:hover,
    .el-menu-item:not(.is-active):hover {
      background: $hover-bg-color !important;
    }

    .el-sub-menu__icon-arrow {
      color: var(--art-gray-600);
    }
  }
}

.dark {
  .layout-sidebar {
    .el-menu-item.is-active {
      span {
        // 暗黑主题模式，选中菜单文字颜色
        color: var(--main-color) !important;
      }

      .menu-icon {
        color: var(--main-color) !important;
      }
    }
  }
}

@media only screen and (max-width: $device-phone) {
  .layout-sidebar {
    .el-menu-design {
      > .el-sub-menu {
        margin-left: 0;
      }

      .el-sub-menu {
        width: 100% !important;
      }
    }
  }
}

// 菜单折叠 hover 弹窗样式
.el-menu--vertical,
.el-menu--popup-container {
  .el-menu--popup {
    padding: 8px;

    .el-sub-menu__title:hover,
    .el-menu-item:hover {
      background-color: var(--art-gray-200) !important;
      border-radius: 6px;
    }

    .el-menu-item {
      height: 40px;
      margin-bottom: 5px;
      border-radius: 6px;

      &:last-of-type {
        margin-bottom: 0;
      }
    }

    .el-sub-menu {
      height: 40px !important;
      margin-bottom: 5px;

      .el-sub-menu__title {
        height: 40px !important;
        border-radius: 6px;
      }

      &:last-of-type {
        margin-bottom: 0;
      }
    }

    .el-menu-item.is-active {
      color: var(--art-gray-900) !important;
      background-color: var(--art-gray-200) !important;
    }
  }
}

// 菜单折叠 hover 弹窗样式（黑色菜单）
.menu-left-dark-popper {
  .el-menu--vertical,
  .el-menu--popup-container {
    .el-menu--popup {
      .el-sub-menu__title:hover,
      .el-menu-item:hover {
        background-color: rgb(255 255 255 / 8%) !important;
      }

      .el-menu-item.is-active {
        color: #eee !important;
        background-color: rgb(255 255 255 / 8%) !important;
      }
    }
  }
}
