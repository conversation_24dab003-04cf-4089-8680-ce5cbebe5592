/**
 * RSA加密工具
 * 用于密码加密，保持与RuoYi一致的加密方式
 */

import JSEncrypt from 'jsencrypt'

// 默认公钥（与RuoYi保持一致）
const publicKey = `-----BEGIN PUBLIC KEY-----
MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQC21ea/7IYKTBtGI2WWtK9JxlSH
YAyenSWiMtVHAd+HdHjmOfQV3hs2pgxQbamhJlZ/ojGm7KBmJfnZWn99X8WcNYs7
+zw2aTfFf9311LWzyrH6665bEjdNlbXxPL7QYF2sVDiVHGphA6YKiJNo4+0HdF1f
BqorG0NtfQxiQB1CkwIDAQAB
-----END PUBLIC KEY-----`

/**
 * RSA加密
 * @param txt 待加密的文本
 * @returns 加密后的文本
 */
export function encrypt(txt: string): string {
  const encryptor = new JSEncrypt()
  encryptor.setPublicKey(publicKey)
  return encryptor.encrypt(txt) || ''
}

/**
 * RSA解密
 * @param txt 待解密的文本
 * @returns 解密后的文本
 */
export function decrypt(txt: string): string {
  const encryptor = new JSEncrypt()
  encryptor.setPublicKey(publicKey)
  return encryptor.decrypt(txt) || ''
}
