<template>
  <div class="post-page art-full-height">
    <!-- 搜索栏 -->
    <ArtSearchBar
      v-model="formFilters"
      :items="formItems"
      :showExpand="false"
      @reset="handleReset"
      @search="handleSearch"
    />

    <ElCard class="art-table-card" shadow="never">
      <!-- 表格头部 -->
      <ArtTableHeader :showZebra="false" v-model:columns="columnChecks" @refresh="handleRefresh">
        <template #left>
          <ElButton v-auth="'system:post:add'" @click="handleAdd" v-ripple>
            新增岗位
          </ElButton>
          <ElButton 
            v-auth="'system:post:edit'" 
            :disabled="single" 
            @click="handleUpdate" 
            v-ripple
          >
            修改
          </ElButton>
          <ElButton 
            v-auth="'system:post:remove'" 
            :disabled="multiple" 
            @click="handleDelete" 
            v-ripple
          >
            删除
          </ElButton>
          <ElButton v-auth="'system:post:export'" @click="handleExport" v-ripple>
            导出
          </ElButton>
        </template>
      </ArtTableHeader>

      <ArtTable
        ref="tableRef"
        rowKey="postId"
        :loading="loading"
        :columns="columns"
        :data="postList"
        :stripe="true"
        @selection-change="handleSelectionChange"
      />

      <!-- 分页组件 -->
      <div class="art-pagination">
        <ElPagination
          v-show="total > 0"
          v-model:current-page="queryParams.pageNum"
          v-model:page-size="queryParams.pageSize"
          :total="total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="getTableData"
          @current-change="getTableData"
        />
      </div>

      <!-- 添加或修改岗位对话框 -->
      <ElDialog :title="dialogTitle" v-model="dialogVisible" width="500px" align-center>
        <ElForm ref="formRef" :model="form" :rules="rules" label-width="80px">
          <ElFormItem label="岗位名称" prop="postName">
            <ElInput v-model="form.postName" placeholder="请输入岗位名称" />
          </ElFormItem>
          <ElFormItem label="岗位编码" prop="postCode">
            <ElInput v-model="form.postCode" placeholder="请输入编码名称" />
          </ElFormItem>
          <ElFormItem label="岗位顺序" prop="postSort">
            <ElInputNumber 
              v-model="form.postSort" 
              controls-position="right" 
              :min="0" 
              style="width: 100%"
            />
          </ElFormItem>
          <ElFormItem label="岗位状态" prop="status">
            <ElRadioGroup v-model="form.status">
              <ElRadio value="0">正常</ElRadio>
              <ElRadio value="1">停用</ElRadio>
            </ElRadioGroup>
          </ElFormItem>
          <ElFormItem label="备注" prop="remark">
            <ElInput 
              v-model="form.remark" 
              type="textarea" 
              placeholder="请输入内容" 
              :rows="3"
            />
          </ElFormItem>
        </ElForm>

        <template #footer>
          <span class="dialog-footer">
            <ElButton @click="dialogVisible = false">取 消</ElButton>
            <ElButton type="primary" @click="submitForm">确 定</ElButton>
          </span>
        </template>
      </ElDialog>
    </ElCard>
  </div>
</template>

<script setup lang="ts">
import { ElMessage, ElMessageBox, ElTag } from 'element-plus'
import { useTableColumns } from '@/composables/useTableColumns'
import { useAuth } from '@/composables/useAuth'
import { PostApi } from '@/api/system/post'
import type { Post, PostQueryParams } from '@/types/system/post'
import type { FormInstance, FormRules } from 'element-plus'
import ArtButtonTable from '@/components/core/forms/art-button-table/index.vue'

defineOptions({ name: 'SystemPost' })

const { hasAuth } = useAuth()

const loading = ref(false)
const dialogVisible = ref(false)
const formRef = ref<FormInstance>()

// 定义表单搜索初始值
const initialSearchState = {
  postCode: '',
  postName: '',
  status: ''
}

// 响应式表单数据
const formFilters = reactive({ ...initialSearchState })

// 岗位数据
const postList = ref<Post[]>([])
const total = ref(0)
const ids = ref<number[]>([])
const single = ref(true)
const multiple = ref(true)

// 查询参数
const queryParams = reactive<PostQueryParams>({
  pageNum: 1,
  pageSize: 10,
  postCode: '',
  postName: '',
  status: ''
})

// 表单数据
const form = reactive<Post>({
  postId: undefined,
  postCode: '',
  postName: '',
  postSort: 0,
  status: '0',
  remark: ''
})

// 表单验证规则
const rules = reactive<FormRules>({
  postName: [{ required: true, message: '岗位名称不能为空', trigger: 'blur' }],
  postCode: [{ required: true, message: '岗位编码不能为空', trigger: 'blur' }],
  postSort: [{ required: true, message: '岗位顺序不能为空', trigger: 'blur' }]
})

const isEdit = ref(false)
const dialogTitle = computed(() => (isEdit.value ? '修改岗位' : '新增岗位'))

// 重置表单
const handleReset = () => {
  Object.assign(formFilters, { ...initialSearchState })
  Object.assign(queryParams, { 
    pageNum: 1, 
    pageSize: 10,
    ...initialSearchState 
  })
  getTableData()
}

// 搜索处理
const handleSearch = () => {
  Object.assign(queryParams, { 
    pageNum: 1,
    ...formFilters 
  })
  getTableData()
}

// 表单配置项
const formItems = computed(() => [
  {
    label: '岗位编码',
    key: 'postCode',
    type: 'input',
    props: { clearable: true }
  },
  {
    label: '岗位名称',
    key: 'postName',
    type: 'input',
    props: { clearable: true }
  },
  {
    label: '状态',
    key: 'status',
    type: 'select',
    props: { clearable: true },
    options: [
      { label: '正常', value: '0' },
      { label: '停用', value: '1' }
    ]
  }
])

// 动态列配置
const { columnChecks, columns } = useTableColumns(() => [
  {
    type: 'selection',
    width: 55
  },
  {
    prop: 'postId',
    label: '岗位编号',
    width: 100
  },
  {
    prop: 'postCode',
    label: '岗位编码',
    minWidth: 120
  },
  {
    prop: 'postName',
    label: '岗位名称',
    minWidth: 120
  },
  {
    prop: 'postSort',
    label: '岗位排序',
    width: 100
  },
  {
    prop: 'status',
    label: '状态',
    width: 100,
    formatter: (row: Post) => {
      return h(ElTag, { type: row.status === '0' ? 'success' : 'danger' }, () =>
        row.status === '0' ? '正常' : '停用'
      )
    }
  },
  {
    prop: 'createTime',
    label: '创建时间',
    width: 180,
    formatter: (row: Post) => row.createTime || '--'
  },
  {
    prop: 'operation',
    label: '操作',
    width: 150,
    formatter: (row: Post) => {
      return h('div', [
        hasAuth('system:post:edit') &&
          h(ArtButtonTable, {
            type: 'edit',
            onClick: () => handleUpdate(row)
          }),
        hasAuth('system:post:remove') &&
          h(ArtButtonTable, {
            type: 'delete',
            onClick: () => handleDelete(row)
          })
      ])
    }
  }
])

onMounted(() => {
  getTableData()
})

const getTableData = async () => {
  loading.value = true
  try {
    const response = await PostApi.getPostList(queryParams)
    postList.value = response.rows || []
    total.value = response.total || 0
  } catch (error) {
    ElMessage.error('获取岗位列表失败')
  } finally {
    loading.value = false
  }
}

const handleRefresh = () => {
  getTableData()
}

// 多选框选中数据
const handleSelectionChange = (selection: Post[]) => {
  ids.value = selection.map(item => item.postId!)
  single.value = selection.length !== 1
  multiple.value = !selection.length
}

// 新增按钮操作
const handleAdd = () => {
  resetForm()
  dialogVisible.value = true
  isEdit.value = false
}

// 修改按钮操作
const handleUpdate = async (row?: Post) => {
  resetForm()
  const postId = row?.postId || ids.value[0]
  
  try {
    const response = await PostApi.getPostDetail(postId)
    Object.assign(form, response.data)
    dialogVisible.value = true
    isEdit.value = true
  } catch (error) {
    ElMessage.error('获取岗位信息失败')
  }
}

// 删除按钮操作
const handleDelete = async (row?: Post) => {
  const postIds = row?.postId ? [row.postId] : ids.value
  
  try {
    await ElMessageBox.confirm(
      `是否确认删除岗位编号为"${postIds.join(',')}"的数据项？`,
      '系统提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await PostApi.deletePost(postIds.length === 1 ? postIds[0] : postIds)
    ElMessage.success('删除成功')
    getTableData()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

// 导出按钮操作
const handleExport = async () => {
  try {
    await PostApi.exportPost(queryParams)
    ElMessage.success('导出成功')
  } catch (error) {
    ElMessage.error('导出失败')
  }
}

// 提交表单
const submitForm = async () => {
  if (!formRef.value) return
  
  await formRef.value.validate(async (valid) => {
    if (valid) {
      try {
        if (form.postId) {
          await PostApi.updatePost(form)
          ElMessage.success('修改成功')
        } else {
          await PostApi.addPost(form)
          ElMessage.success('新增成功')
        }
        
        dialogVisible.value = false
        getTableData()
      } catch (error) {
        ElMessage.error(isEdit.value ? '修改失败' : '新增失败')
      }
    }
  })
}

// 重置表单
const resetForm = () => {
  Object.assign(form, {
    postId: undefined,
    postCode: '',
    postName: '',
    postSort: 0,
    status: '0',
    remark: ''
  })
  formRef.value?.resetFields()
}
</script>

<style lang="scss" scoped>
.post-page {
  .dialog-footer {
    text-align: right;
  }
  
  .art-pagination {
    margin-top: 16px;
    display: flex;
    justify-content: flex-end;
  }
}
</style>
