/**
 * 登录认证API
 * 保持RuoYi接口格式，直接支持RuoYi后端接口
 */

import { http } from '@/utils/http'
import type { RuoyiResponse } from '@/types/http'

/** 登录表单数据 */
export interface LoginForm {
  /** 用户名 */
  username: string
  /** 密码 */
  password: string
  /** 验证码 */
  code?: string
  /** 验证码唯一标识 */
  uuid?: string
  /** 记住密码 */
  rememberMe?: boolean
}

/** 登录响应数据 */
export interface LoginResult {
  /** 访问令牌 */
  token: string
}

/** 验证码响应数据 */
export interface CaptchaData {
  /** 是否开启验证码 */
  captchaEnabled: boolean
  /** 验证码图片base64 */
  img?: string
  /** 验证码唯一标识 */
  uuid?: string
}

/** 用户信息响应数据 */
export interface UserInfoResult {
  /** 用户基本信息 */
  user: {
    userId: number
    userName: string
    nickName: string
    email?: string
    phonenumber?: string
    sex?: string
    avatar?: string
    deptId?: number
    deptName?: string
    postIds?: number[]
    roleIds?: number[]
  }
  /** 角色列表 */
  roles: string[]
  /** 权限列表 */
  permissions: string[]
}

/**
 * 登录认证API类
 * 完全保持RuoYi的登录接口格式
 */
export class LoginApi {
  /**
   * 用户登录
   * @param loginForm 登录表单数据
   * @returns 登录结果
   */
  static async login(loginForm: LoginForm): Promise<RuoyiResponse<LoginResult>> {
    return http({
      url: '/login',
      method: 'post',
      data: {
        username: loginForm.username,
        password: loginForm.password,
        code: loginForm.code,
        uuid: loginForm.uuid
      },
      headers: {
        isToken: false,
        repeatSubmit: false
      }
    })
  }

  /**
   * 获取验证码
   * @returns 验证码数据
   */
  static async getCaptchaImage(): Promise<RuoyiResponse<CaptchaData>> {
    return http({
      url: '/captchaImage',
      method: 'get',
      headers: {
        isToken: false
      },
      timeout: 20000
    })
  }

  /**
   * 获取用户信息
   * @returns 用户信息
   */
  static async getUserInfo(): Promise<RuoyiResponse<UserInfoResult>> {
    return http({
      url: '/getInfo',
      method: 'get'
    })
  }

  /**
   * 用户注册
   * @param registerData 注册数据
   * @returns 注册结果
   */
  static async register(registerData: any): Promise<RuoyiResponse> {
    return http({
      url: '/register',
      method: 'post',
      data: registerData,
      headers: {
        isToken: false
      }
    })
  }

  /**
   * 用户退出
   * @returns 退出结果
   */
  static async logout(): Promise<RuoyiResponse> {
    return http({
      url: '/logout',
      method: 'post'
    })
  }
}

// 兼容性导出，保持与RuoYi原有调用方式一致
export const login = LoginApi.login
export const getCodeImg = LoginApi.getCaptchaImage
export const getInfo = LoginApi.getUserInfo
export const register = LoginApi.register
export const logout = LoginApi.logout
