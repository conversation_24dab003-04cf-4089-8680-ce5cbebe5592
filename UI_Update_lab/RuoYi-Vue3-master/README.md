# RuoYi Vue3 管理系统

<p align="center">
	<img alt="logo" src="https://oscimg.oschina.net/oscnet/up-d3d0a9303e11d522a06cd263f3079027715.png">
</p>

<h1 align="center" style="margin: 30px 0 30px; font-weight: bold;">RuoYi v3.9.0</h1>
<h4 align="center">基于 Vue3 + Element Plus + Vite 的现代化前端管理系统</h4>

<p align="center">
	<a href="https://gitee.com/y_project/RuoYi-Vue/stargazers"><img src="https://gitee.com/y_project/RuoYi-Vue/badge/star.svg?theme=dark"></a>
	<a href="https://gitee.com/y_project/RuoYi-Vue"><img src="https://img.shields.io/badge/RuoYi-v3.9.0-brightgreen.svg"></a>
	<a href="https://gitee.com/y_project/RuoYi-Vue/blob/master/LICENSE"><img src="https://img.shields.io/github/license/mashape/apistatus.svg"></a>
</p>

## 📖 项目简介

RuoYi Vue3 是一套基于 **Vue3** + **Element Plus** + **Vite** 构建的现代化企业级管理系统前端解决方案。采用最新的前端技术栈，提供完整的权限管理、系统监控、代码生成等功能模块，是快速开发企业级应用的理想选择。

### ✨ 技术特色

- 🚀 **Vue3 Composition API** - 采用最新的 Vue3 语法，更好的 TypeScript 支持
- ⚡ **Vite 构建工具** - 极速的开发体验，HMR 热更新
- 🎨 **Element Plus** - 基于 Vue3 的组件库，界面美观易用
- 📱 **响应式设计** - 支持多终端适配
- 🔐 **完整权限系统** - 基于 RBAC 的权限控制
- 📊 **丰富图表** - 集成 ECharts 数据可视化
- 🛠️ **代码生成器** - 一键生成前后端代码

## 🛠️ 技术栈

| 技术 | 版本 | 说明 |
|------|------|------|
| Vue | 3.5.16 | 渐进式 JavaScript 框架 |
| Element Plus | 2.10.7 | Vue3 组件库 |
| Vite | 6.3.5 | 前端构建工具 |
| Vue Router | 4.5.1 | 路由管理 |
| Pinia | 3.0.2 | 状态管理 |
| Axios | 1.9.0 | HTTP 客户端 |
| ECharts | 5.6.0 | 数据可视化 |
| Sass | 1.89.1 | CSS 预处理器 |

## 🚀 快速开始

### 环境要求

- Node.js >= 16.0.0
- npm >= 7.0.0 或 yarn >= 1.22.0

### 安装依赖

```bash
# 克隆项目
git clone https://github.com/yangzongzhuan/RuoYi-Vue3.git

# 进入项目目录
cd RuoYi-Vue3

# 安装依赖
npm install
# 或使用 yarn
yarn install
# 或使用国内镜像
yarn --registry=https://registry.npmmirror.com
```

### 开发调试

```bash
# 启动开发服务器
npm run dev
# 或
yarn dev

# 访问地址：http://localhost:80
```

### 构建部署

```bash
# 构建生产环境
npm run build:prod

# 构建测试环境
npm run build:stage

# 预览构建结果
npm run preview
```

## 📁 项目结构

```
RuoYi-Vue3/
├── public/                 # 静态资源
├── src/
│   ├── api/               # API 接口
│   ├── assets/            # 静态资源
│   │   ├── icons/         # 图标
│   │   ├── images/        # 图片
│   │   └── styles/        # 样式文件
│   ├── components/        # 全局组件
│   │   ├── Pagination/    # 分页组件
│   │   ├── RightToolbar/  # 工具栏组件
│   │   ├── Editor/        # 富文本编辑器
│   │   ├── FileUpload/    # 文件上传
│   │   └── ...
│   ├── directive/         # 自定义指令
│   ├── layout/            # 布局组件
│   ├── plugins/           # 插件
│   ├── router/            # 路由配置
│   ├── store/             # 状态管理
│   ├── utils/             # 工具函数
│   ├── views/             # 页面组件
│   │   ├── system/        # 系统管理
│   │   ├── monitor/       # 系统监控
│   │   ├── tool/          # 系统工具
│   │   └── ...
│   ├── App.vue            # 根组件
│   ├── main.js            # 入口文件
│   └── permission.js      # 权限控制
├── vite/                  # Vite 配置
├── .env.development       # 开发环境变量
├── .env.production        # 生产环境变量
├── vite.config.js         # Vite 配置文件
└── package.json           # 项目配置
```

## 🔧 核心功能

### 系统管理
- **用户管理** - 系统用户的增删改查、角色分配
- **部门管理** - 组织架构管理，支持树形结构
- **岗位管理** - 岗位信息维护
- **菜单管理** - 系统菜单配置、权限控制
- **角色管理** - 角色权限分配、数据权限设置
- **字典管理** - 系统字典数据维护
- **参数管理** - 系统参数配置

### 系统监控
- **在线用户** - 当前在线用户监控
- **定时任务** - Quartz 定时任务管理
- **数据监控** - Druid 数据库连接池监控
- **服务监控** - 系统运行状态监控
- **缓存监控** - Redis 缓存监控

### 系统工具
- **表单构建** - 拖拽式表单设计器
- **代码生成** - 前后端代码自动生成
- **系统接口** - Swagger API 文档

### 日志管理
- **操作日志** - 用户操作日志记录
- **登录日志** - 用户登录日志记录

## 🎨 界面预览

<table>
    <tr>
        <td><img src="https://oscimg.oschina.net/oscnet/cd1f90be5f2684f4560c9519c0f2a232ee8.jpg"/></td>
        <td><img src="https://oscimg.oschina.net/oscnet/1cbcf0e6f257c7d3a063c0e3f2ff989e4b3.jpg"/></td>
    </tr>
    <tr>
        <td><img src="https://oscimg.oschina.net/oscnet/up-8074972883b5ba0622e13246738ebba237a.png"/></td>
        <td><img src="https://oscimg.oschina.net/oscnet/up-9f88719cdfca9af2e58b352a20e23d43b12.png"/></td>
    </tr>
</table>

## 🌐 在线体验

- **演示地址：** [http://vue.ruoyi.vip](http://vue.ruoyi.vip)
- **文档地址：** [http://doc.ruoyi.vip](http://doc.ruoyi.vip)
- **账号密码：** admin/admin123

## 🔗 相关项目

- **后端项目：** [RuoYi-Vue](https://gitee.com/y_project/RuoYi-Vue)
- **Vue2 版本：** [RuoYi-Vue2](https://gitee.com/y_project/RuoYi-Vue/tree/master/ruoyi-ui)
- **微服务版本：** [RuoYi-Cloud](https://gitee.com/y_project/RuoYi-Cloud)

## 📝 开发规范

### 代码规范
- 使用 ESLint 进行代码检查
- 遵循 Vue3 Composition API 最佳实践
- 组件命名采用 PascalCase
- 文件命名采用 kebab-case

### Git 提交规范
```
feat: 新功能
fix: 修复bug
docs: 文档更新
style: 代码格式调整
refactor: 代码重构
test: 测试相关
chore: 构建过程或辅助工具的变动
```

## 🤝 参与贡献

1. Fork 本仓库
2. 新建 feature/xxx 分支
3. 提交代码
4. 新建 Pull Request

## 📄 开源协议

本项目基于 [MIT](LICENSE) 协议，请自由地享受和参与开源。

## 💬 交流群

欢迎加入若依交流群，一起学习交流：

- QQ群：937441、887144332、180251782 等
- 更多群号请查看官方文档

## ⭐ Star History

如果这个项目对你有帮助，请给我们一个 ⭐️

---

<p align="center">
  <a href="https://gitee.com/y_project/RuoYi-Vue">Gitee</a> |
  <a href="https://github.com/yangzongzhuan/RuoYi-Vue3">GitHub</a> |
  <a href="http://doc.ruoyi.vip">文档</a> |
  <a href="http://vue.ruoyi.vip">演示</a>
</p>