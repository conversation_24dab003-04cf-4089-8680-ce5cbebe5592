import { App, Directive, DirectiveBinding } from 'vue'
import { useAuth } from '@/composables/useAuth'

/**
 * 角色权限指令
 * 直接支持RuoYi角色标识格式
 * 只要用户角色包含指令值中的任意一个角色，则显示元素
 * 用法：
 * <el-button v-roles="['admin', 'common']">按钮</el-button>
 * <el-button v-roles="'admin'">按钮</el-button>
 */

interface RolesBinding extends DirectiveBinding {
  value: string | string[]
}

function checkRolePermission(el: HTMLElement, binding: RolesBinding): void {
  const { hasRole, hasRoleOr } = useAuth()
  let hasPermission = false

  if (typeof binding.value === 'string') {
    // 单个角色验证
    hasPermission = hasRole(binding.value)
  } else if (Array.isArray(binding.value)) {
    // 多个角色验证（只需要其中一个）
    hasPermission = hasRoleOr(binding.value)
  }

  // 如果没有权限，移除元素
  if (!hasPermission) {
    removeElement(el)
  }
}

function removeElement(el: HTMLElement): void {
  if (el.parentNode) {
    el.parentNode.removeChild(el)
  }
}

const rolesDirective: Directive = {
  mounted: checkRolePermission,
  updated: checkRolePermission
}

export function setupRolesDirective(app: App): void {
  app.directive('roles', rolesDirective)
}
