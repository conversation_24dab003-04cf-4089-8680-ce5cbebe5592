import { http } from '@/utils/http'
import type { RuoyiResponse } from '@/types/http'
import type { User, UserQueryParams } from '@/types/system/user'
import type { Dept } from '@/types/system/dept'
import type { Role } from '@/types/system/role'
import type { Post } from '@/types/system/post'

/**
 * 用户管理API类
 * 完全保持RuoYi的用户接口格式
 */
export class UserApi {
  /**
   * 查询用户列表
   * @param params 查询参数
   * @returns 用户列表响应
   */
  static async getUserList(params: UserQueryParams): Promise<RuoyiResponse<User>> {
    return http.get('/system/user/list', params)
  }

  /**
   * 查询用户详细信息
   * @param userId 用户ID
   * @returns 用户详情响应
   */
  static async getUser(userId: number): Promise<
    RuoyiResponse<{
      data: User
      posts: Post[]
      roles: Role[]
      postIds: number[]
      roleIds: number[]
    }>
  > {
    return http.get(`/system/user/${userId}`)
  }

  /**
   * 新增用户
   * @param data 用户数据
   * @returns 操作结果
   */
  static async addUser(data: Partial<User>): Promise<RuoyiResponse> {
    return http.post('/system/user', data)
  }

  /**
   * 修改用户
   * @param data 用户数据
   * @returns 操作结果
   */
  static async updateUser(data: Partial<User>): Promise<RuoyiResponse> {
    return http.put('/system/user', data)
  }

  /**
   * 删除用户
   * @param userIds 用户ID数组
   * @returns 操作结果
   */
  static async delUser(userIds: number | number[]): Promise<RuoyiResponse> {
    const ids = Array.isArray(userIds) ? userIds.join(',') : userIds
    return http.delete(`/system/user/${ids}`)
  }

  /**
   * 重置用户密码
   * @param userId 用户ID
   * @param password 新密码
   * @returns 操作结果
   */
  static async resetUserPwd(userId: number, password: string): Promise<RuoyiResponse> {
    return http.put('/system/user/resetPwd', { userId, password })
  }

  /**
   * 修改用户状态
   * @param userId 用户ID
   * @param status 状态（0正常 1停用）
   * @returns 操作结果
   */
  static async changeUserStatus(userId: number, status: string): Promise<RuoyiResponse> {
    return http.put('/system/user/changeStatus', { userId, status })
  }

  /**
   * 查询部门下拉树结构
   * @returns 部门树响应
   */
  static async deptTreeSelect(): Promise<RuoyiResponse<Dept[]>> {
    return http.get('/system/user/deptTree')
  }

  /**
   * 查询授权角色
   * @param userId 用户ID
   * @returns 授权角色响应
   */
  static async getAuthRole(userId: number): Promise<
    RuoyiResponse<{
      user: User
      roles: Role[]
    }>
  > {
    return http.get(`/system/user/authRole/${userId}`)
  }

  /**
   * 保存授权角色
   * @param data 授权数据
   * @returns 操作结果
   */
  static async updateAuthRole(data: { userId: number; roleIds: string }): Promise<RuoyiResponse> {
    return http.put('/system/user/authRole', null, { params: data })
  }

  /**
   * 用户密码重置
   * @param oldPassword 旧密码
   * @param newPassword 新密码
   * @returns 操作结果
   */
  static async updateUserPwd(oldPassword: string, newPassword: string): Promise<RuoyiResponse> {
    return http.put('/system/user/profile/updatePwd', { oldPassword, newPassword })
  }

  /**
   * 用户头像上传
   * @param data 头像数据
   * @returns 操作结果
   */
  static async uploadAvatar(data: FormData): Promise<RuoyiResponse<{ imgUrl: string }>> {
    return http.post('/system/user/profile/avatar', data, {
      headers: { 'Content-Type': 'multipart/form-data' }
    })
  }

  /**
   * 导出用户数据
   * @param params 查询参数
   * @returns 文件流
   */
  static async exportUser(params: UserQueryParams): Promise<Blob> {
    return http.get('/system/user/export', params, { responseType: 'blob' })
  }

  /**
   * 下载用户导入模板
   * @returns 文件流
   */
  static async importTemplate(): Promise<Blob> {
    return http.get('/system/user/importTemplate', {}, { responseType: 'blob' })
  }

  /**
   * 用户数据导入
   * @param data 导入数据
   * @param updateSupport 是否更新已存在数据
   * @returns 操作结果
   */
  static async importData(
    data: FormData,
    updateSupport: boolean = false
  ): Promise<RuoyiResponse<string>> {
    return http.post(`/system/user/importData?updateSupport=${updateSupport}`, data, {
      headers: { 'Content-Type': 'multipart/form-data' }
    })
  }
}
