import axios, { AxiosRequestConfig, AxiosResponse, InternalAxiosRequestConfig } from 'axios'
import { ElMessage, ElMessageBox } from 'element-plus'
import { RuoyiResponse, RuoyiStatusCode } from '@/types/http'
import { getToken } from '@/utils/auth'
import { useUserStore } from '@/store/modules/user'

/** 请求配置常量 */
const REQUEST_TIMEOUT = 10000

/** 重新登录状态控制 */
export const isRelogin = { show: false }

/** 扩展 AxiosRequestConfig 支持RuoYi配置 */
interface ExtendedAxiosRequestConfig extends AxiosRequestConfig {
  showErrorMessage?: boolean
  isToken?: boolean
  repeatSubmit?: boolean
}

/**
 * 参数转换工具函数
 */
function tansParams(params: any): string {
  let result = ''
  for (const propName of Object.keys(params)) {
    const value = params[propName]
    const part = encodeURIComponent(propName) + '='
    if (value !== null && value !== '' && typeof value !== 'undefined') {
      if (typeof value === 'object') {
        for (const key of Object.keys(value)) {
          if (value[key] !== null && value[key] !== '' && typeof value[key] !== 'undefined') {
            const params = propName + '[' + key + ']'
            const subPart = encodeURIComponent(params) + '='
            result += subPart + encodeURIComponent(value[key]) + '&'
          }
        }
      } else {
        result += part + encodeURIComponent(value) + '&'
      }
    }
  }
  return result
}

/** 环境变量 */
const { VITE_APP_BASE_API } = import.meta.env

/** 设置默认请求头 */
axios.defaults.headers['Content-Type'] = 'application/json;charset=utf-8'

/** 创建axios实例 - 直接支持RuoYi格式 */
const service = axios.create({
  baseURL: VITE_APP_BASE_API,
  timeout: REQUEST_TIMEOUT
})

/** 请求拦截器 - 直接支持RuoYi配置 */
service.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    // 是否需要设置token
    const isToken = (config.headers || {}).isToken === false

    if (getToken() && !isToken) {
      config.headers['Authorization'] = 'Bearer ' + getToken()
    }

    // GET请求参数处理
    if (config.method === 'get' && config.params) {
      let url = config.url + '?' + tansParams(config.params)
      url = url.slice(0, -1)
      config.params = {}
      config.url = url
    }

    return config
  },
  (error: any) => {
    console.log(error)
    return Promise.reject(error)
  }
)

/** 响应拦截器 - 直接处理RuoYi响应格式 */
service.interceptors.response.use(
  (res: AxiosResponse<RuoyiResponse>) => {
    // 未设置状态码则默认成功状态
    const code = res.data.code || RuoyiStatusCode.SUCCESS
    const msg = res.data.msg || '操作成功'

    // 二进制数据则直接返回原始响应
    if (res.request.responseType === 'blob' || res.request.responseType === 'arraybuffer') {
      return res
    }

    // 处理不同状态码
    if (code === RuoyiStatusCode.UNAUTHORIZED) {
      handleUnauthorizedError()
      return Promise.reject('无效的会话，或者会话已过期，请重新登录。')
    } else if (code === RuoyiStatusCode.SERVER_ERROR) {
      ElMessage({ message: msg, type: 'error' })
      return Promise.reject(new Error(msg))
    } else if (code === RuoyiStatusCode.WARNING) {
      ElMessage({ message: msg, type: 'warning' })
      return Promise.reject(new Error(msg))
    } else if (code !== RuoyiStatusCode.SUCCESS) {
      ElMessage({ message: msg, type: 'error' })
      return Promise.reject('error')
    } else {
      // 成功时返回修改后的响应，将RuoYi数据作为响应体
      return { ...res, data: res.data } as AxiosResponse<RuoyiResponse>
    }
  },
  (error: any) => {
    console.log('err' + error)
    let { message } = error
    if (message === 'Network Error') {
      message = '后端接口连接异常'
    } else if (message.includes('timeout')) {
      message = '系统接口请求超时'
    } else if (message.includes('Request failed with status code')) {
      message = '系统接口' + message.substr(message.length - 3) + '异常'
    }
    ElMessage({ message: message, type: 'error', duration: 5 * 1000 })
    return Promise.reject(error)
  }
)

/**
 * 处理401未授权错误
 */
function handleUnauthorizedError(): void {
  if (!isRelogin.show) {
    isRelogin.show = true
    ElMessageBox.confirm('登录状态已过期，您可以继续留在该页面，或者重新登录', '系统提示', {
      confirmButtonText: '重新登录',
      cancelButtonText: '取消',
      type: 'warning'
    })
      .then(() => {
        isRelogin.show = false
        useUserStore().logOut()
        location.href = '/index'
      })
      .catch(() => {
        isRelogin.show = false
      })
  }
}

/**
 * HTTP客户端类 - 直接支持RuoYi格式
 */
export class HttpClient {
  /**
   * GET请求
   */
  static async get<T = any>(url: string, params?: any): Promise<RuoyiResponse<T>> {
    return service.get(url, { params })
  }

  /**
   * POST请求
   */
  static async post<T = any>(url: string, data?: any): Promise<RuoyiResponse<T>> {
    return service.post(url, data)
  }

  /**
   * PUT请求
   */
  static async put<T = any>(url: string, data?: any): Promise<RuoyiResponse<T>> {
    return service.put(url, data)
  }

  /**
   * DELETE请求
   */
  static async delete<T = any>(url: string): Promise<RuoyiResponse<T>> {
    return service.delete(url)
  }

  /**
   * 通用请求方法
   */
  static async request<T = any>(config: ExtendedAxiosRequestConfig): Promise<RuoyiResponse<T>> {
    return service.request(config)
  }
}

// 导出默认的service实例和HttpClient类
export { service as http }
export default HttpClient
