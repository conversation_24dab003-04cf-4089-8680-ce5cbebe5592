/**
 * 系统管理API类型定义
 * 基于RuoYi数据结构，保持字段名称与RuoYi一致
 */

import type { RuoyiResponse } from '@/types/http'
import type {
  User,
  Dept,
  Role,
  Post,
  Menu,
  DictType,
  DictData,
  Config,
  Notice,
  LoginResponse as SystemLoginResponse,
  CaptchaResponse as SystemCaptchaResponse,
  UserInfoResponse as SystemUserInfoResponse,
  OnlineUser,
  OperLog,
  LoginInfo,
  ServerInfo,
  CacheInfo,
  MenuTreeOption,
  RoleMenuTreeSelectResponse,
  DictOption
} from '@/types/system'

/** 用户管理API类型 */
export namespace UserApi {
  /** 用户列表响应 */
  export type ListResponse = RuoyiResponse<User>

  /** 用户详情响应 */
  export type DetailResponse = RuoyiResponse<{
    data: User
    posts: Post[]
    roles: Role[]
    postIds: number[]
    roleIds: number[]
  }>

  /** 用户新增/编辑响应 */
  export type SaveResponse = RuoyiResponse<null>

  /** 用户删除响应 */
  export type DeleteResponse = RuoyiResponse<null>

  /** 用户状态修改响应 */
  export type StatusResponse = RuoyiResponse<null>

  /** 重置密码响应 */
  export type ResetPwdResponse = RuoyiResponse<null>

  /** 部门树响应 */
  export type DeptTreeResponse = RuoyiResponse<Dept[]>
}

/** 角色管理API类型 */
export namespace RoleApi {
  /** 角色列表响应 */
  export type ListResponse = RuoyiResponse<Role>

  /** 角色详情响应 */
  export type DetailResponse = RuoyiResponse<Role>

  /** 角色新增/编辑响应 */
  export type SaveResponse = RuoyiResponse<null>

  /** 角色删除响应 */
  export type DeleteResponse = RuoyiResponse<null>

  /** 角色状态修改响应 */
  export type StatusResponse = RuoyiResponse<null>

  /** 数据权限响应 */
  export type DataScopeResponse = RuoyiResponse<null>

  /** 角色菜单树响应 */
  export type MenuTreeResponse = RuoyiResponse<RoleMenuTreeSelectResponse>

  /** 角色部门树响应 */
  export type DeptTreeResponse = RuoyiResponse<{
    depts: Dept[]
    checkedKeys: number[]
  }>
}

/** 菜单管理API类型 */
export namespace MenuApi {
  /** 菜单列表响应 */
  export type ListResponse = RuoyiResponse<Menu[]>

  /** 菜单详情响应 */
  export type DetailResponse = RuoyiResponse<Menu>

  /** 菜单新增/编辑响应 */
  export type SaveResponse = RuoyiResponse<null>

  /** 菜单删除响应 */
  export type DeleteResponse = RuoyiResponse<null>

  /** 菜单树响应 */
  export type TreeResponse = RuoyiResponse<MenuTreeOption[]>

  /** 路由信息响应 */
  export type RoutersResponse = RuoyiResponse<Menu[]>
}

/** 部门管理API类型 */
export namespace DeptApi {
  /** 部门列表响应 */
  export type ListResponse = RuoyiResponse<Dept[]>

  /** 部门详情响应 */
  export type DetailResponse = RuoyiResponse<Dept>

  /** 部门新增/编辑响应 */
  export type SaveResponse = RuoyiResponse<null>

  /** 部门删除响应 */
  export type DeleteResponse = RuoyiResponse<null>

  /** 部门树响应 */
  export type TreeResponse = RuoyiResponse<Dept[]>
}

/** 岗位管理API类型 */
export namespace PostApi {
  /** 岗位列表响应 */
  export type ListResponse = RuoyiResponse<Post>

  /** 岗位详情响应 */
  export type DetailResponse = RuoyiResponse<Post>

  /** 岗位新增/编辑响应 */
  export type SaveResponse = RuoyiResponse<null>

  /** 岗位删除响应 */
  export type DeleteResponse = RuoyiResponse<null>
}

/** 字典管理API类型 */
export namespace DictApi {
  /** 字典类型列表响应 */
  export type TypeListResponse = RuoyiResponse<DictType>

  /** 字典类型详情响应 */
  export type TypeDetailResponse = RuoyiResponse<DictType>

  /** 字典数据列表响应 */
  export type DataListResponse = RuoyiResponse<DictData>

  /** 字典数据详情响应 */
  export type DataDetailResponse = RuoyiResponse<DictData>

  /** 字典选项响应 */
  export type OptionsResponse = RuoyiResponse<DictOption[]>

  /** 字典新增/编辑响应 */
  export type SaveResponse = RuoyiResponse<null>

  /** 字典删除响应 */
  export type DeleteResponse = RuoyiResponse<null>
}

/** 参数配置API类型 */
export namespace ConfigApi {
  /** 参数列表响应 */
  export type ListResponse = RuoyiResponse<Config>

  /** 参数详情响应 */
  export type DetailResponse = RuoyiResponse<Config>

  /** 参数新增/编辑响应 */
  export type SaveResponse = RuoyiResponse<null>

  /** 参数删除响应 */
  export type DeleteResponse = RuoyiResponse<null>

  /** 参数键值响应 */
  export type KeyResponse = RuoyiResponse<string>
}

/** 通知公告API类型 */
export namespace NoticeApi {
  /** 公告列表响应 */
  export type ListResponse = RuoyiResponse<Notice>

  /** 公告详情响应 */
  export type DetailResponse = RuoyiResponse<Notice>

  /** 公告新增/编辑响应 */
  export type SaveResponse = RuoyiResponse<null>

  /** 公告删除响应 */
  export type DeleteResponse = RuoyiResponse<null>
}

/** 认证API类型 */
export namespace AuthApi {
  /** 登录响应 */
  export type LoginResponse = RuoyiResponse<SystemLoginResponse>

  /** 验证码响应 */
  export type CaptchaResponse = RuoyiResponse<SystemCaptchaResponse>

  /** 用户信息响应 */
  export type UserInfoResponse = RuoyiResponse<SystemUserInfoResponse>

  /** 注册响应 */
  export type RegisterResponse = RuoyiResponse<null>

  /** 退出响应 */
  export type LogoutResponse = RuoyiResponse<null>
}

/** 监控API类型 */
export namespace MonitorApi {
  /** 在线用户列表响应 */
  export type OnlineListResponse = RuoyiResponse<OnlineUser>

  /** 操作日志列表响应 */
  export type OperLogListResponse = RuoyiResponse<OperLog>

  /** 登录日志列表响应 */
  export type LoginLogListResponse = RuoyiResponse<LoginInfo>

  /** 服务器信息响应 */
  export type ServerInfoResponse = RuoyiResponse<ServerInfo>

  /** 缓存信息响应 */
  export type CacheInfoResponse = RuoyiResponse<CacheInfo[]>
}
