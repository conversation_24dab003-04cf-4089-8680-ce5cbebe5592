# 技术实施详细指南

## 文件映射关系

### 源文件到目标文件映射表

| 功能模块 | RuoYi-Vue3 源文件 | art-design-pro 目标文件 | 备注 |
|----------|-------------------|-------------------------|------|
| **用户管理** |
| 页面组件 | `src/views/system/user/index.vue` | `src/views/system/user/index.vue` | 需完全重写 |
| 用户详情 | `src/views/system/user/profile/` | `src/views/system/user/profile/` | 可选迁移 |
| 角色分配 | `src/views/system/user/authRole.vue` | `src/views/system/user/modules/auth-role.vue` | 重构为子组件 |
| API接口 | `src/api/system/user.js` | `src/api/system/user.ts` | JS转TS |
| **角色管理** |
| 页面组件 | `src/views/system/role/index.vue` | `src/views/system/role/index.vue` | 需完全重写 |
| 用户分配 | `src/views/system/role/authUser.vue` | `src/views/system/role/modules/auth-user.vue` | 重构为子组件 |
| 用户选择 | `src/views/system/role/selectUser.vue` | `src/views/system/role/modules/select-user.vue` | 重构为子组件 |
| API接口 | `src/api/system/role.js` | `src/api/system/role.ts` | JS转TS |
| **菜单管理** |
| 页面组件 | `src/views/system/menu/index.vue` | `src/views/system/menu/index.vue` | 需完全重写 |
| API接口 | `src/api/system/menu.js` | `src/api/system/menu.ts` | JS转TS |
| **部门管理** |
| 页面组件 | `src/views/system/dept/index.vue` | `src/views/system/dept/index.vue` | 需完全重写 |
| API接口 | `src/api/system/dept.js` | `src/api/system/dept.ts` | JS转TS |
| **岗位管理** |
| 页面组件 | `src/views/system/post/index.vue` | `src/views/system/post/index.vue` | 需完全重写 |
| API接口 | `src/api/system/post.js` | `src/api/system/post.ts` | JS转TS |
| **字典管理** |
| 字典类型 | `src/views/system/dict/index.vue` | `src/views/system/dict/index.vue` | 需完全重写 |
| 字典数据 | `src/views/system/dict/data.vue` | `src/views/system/dict/data.vue` | 需完全重写 |
| API接口 | `src/api/system/dict/` | `src/api/system/dict/` | 目录结构保持 |
| **参数管理** |
| 页面组件 | `src/views/system/config/index.vue` | `src/views/system/config/index.vue` | 需完全重写 |
| API接口 | `src/api/system/config.js` | `src/api/system/config.ts` | JS转TS |

## 代码转换规则

### 1. JavaScript 到 TypeScript 转换

#### 基本语法转换
```javascript
// RuoYi 原始代码 (JavaScript)
<script setup name="User">
import { listUser, getUser, delUser, addUser, updateUser } from "@/api/system/user"

const { proxy } = getCurrentInstance()
const userList = ref([])
const loading = ref(true)

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    userName: undefined,
    phonenumber: undefined,
    status: undefined
  }
})

function getList() {
  loading.value = true
  listUser(proxy.addDateRange(queryParams.value, dateRange.value)).then(response => {
    userList.value = response.rows
    total.value = response.total
    loading.value = false
  })
}
</script>
```

```typescript
// art-design-pro 目标代码 (TypeScript)
<script setup lang="ts" name="User">
import { UserApi } from '@/api/system/user'
import type { Api } from '@/types'

defineOptions({ name: 'SystemUser' })

// 类型定义
interface QueryParams {
  pageNum: number
  pageSize: number
  userName?: string
  phonenumber?: string
  status?: string
  beginTime?: string
  endTime?: string
}

interface FormData extends Partial<Api.System.User> {}

// 响应式数据
const userList = ref<Api.System.User[]>([])
const loading = ref<boolean>(true)
const total = ref<number>(0)

const queryParams = reactive<QueryParams>({
  pageNum: 1,
  pageSize: 10,
  userName: undefined,
  phonenumber: undefined,
  status: undefined
})

const formData = reactive<FormData>({})

// 方法定义
const getList = async (): Promise<void> => {
  try {
    loading.value = true
    const response = await UserApi.getUserList(queryParams)
    userList.value = response.rows
    total.value = response.total
  } catch (error) {
    console.error('获取用户列表失败:', error)
  } finally {
    loading.value = false
  }
}
</script>
```

### 2. 组件结构转换

#### 表格组件转换
```vue
<!-- RuoYi 原始表格 -->
<el-table v-loading="loading" :data="userList">
  <el-table-column label="用户编号" align="center" prop="userId" />
  <el-table-column label="用户名称" align="center" prop="userName" :show-overflow-tooltip="true" />
  <el-table-column label="用户昵称" align="center" prop="nickName" :show-overflow-tooltip="true" />
  <el-table-column label="部门" align="center" prop="dept.deptName" :show-overflow-tooltip="true" />
  <el-table-column label="手机号码" align="center" prop="phonenumber" width="120" />
  <el-table-column label="状态" align="center" key="status">
    <template #default="scope">
      <el-switch
        v-model="scope.row.status"
        active-value="0"
        inactive-value="1"
        @change="handleStatusChange(scope.row)"
      ></el-switch>
    </template>
  </el-table-column>
  <el-table-column label="创建时间" align="center" prop="createTime" width="160">
    <template #default="scope">
      <span>{{ parseTime(scope.row.createTime) }}</span>
    </template>
  </el-table-column>
  <el-table-column label="操作" align="center" width="160" class-name="small-padding fixed-width">
    <template #default="scope">
      <el-tooltip content="修改" placement="top" v-if="scope.row.userId !== 1">
        <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['system:user:edit']"></el-button>
      </el-tooltip>
      <el-tooltip content="删除" placement="top" v-if="scope.row.userId !== 1">
        <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['system:user:remove']"></el-button>
      </el-tooltip>
    </template>
  </el-table-column>
</el-table>
```

```vue
<!-- art-design-pro 目标表格 -->
<ArtTable
  :loading="loading"
  :data="userList"
  :columns="columns"
  :pagination="pagination"
  @selection-change="handleSelectionChange"
  @pagination:size-change="handleSizeChange"
  @pagination:current-change="handleCurrentChange"
>
</ArtTable>

<script setup lang="ts">
// 表格列配置
const columns = computed(() => [
  { type: 'selection', width: 50 },
  { prop: 'userId', label: '用户编号', width: 100 },
  { prop: 'userName', label: '用户名称', showOverflowTooltip: true },
  { prop: 'nickName', label: '用户昵称', showOverflowTooltip: true },
  { prop: 'dept.deptName', label: '部门', showOverflowTooltip: true },
  { prop: 'phonenumber', label: '手机号码', width: 120 },
  {
    prop: 'status',
    label: '状态',
    width: 100,
    render: (row: Api.System.User) => h(ElSwitch, {
      modelValue: row.status === '0',
      activeValue: '0',
      inactiveValue: '1',
      onChange: () => handleStatusChange(row)
    })
  },
  {
    prop: 'createTime',
    label: '创建时间',
    width: 160,
    render: (row: Api.System.User) => formatTime(row.createTime)
  },
  {
    prop: 'actions',
    label: '操作',
    width: 160,
    fixed: 'right',
    render: (row: Api.System.User) => [
      h(ElButton, {
        type: 'primary',
        link: true,
        icon: 'Edit',
        onClick: () => handleUpdate(row),
        disabled: row.userId === 1
      }, '修改'),
      h(ElButton, {
        type: 'danger',
        link: true,
        icon: 'Delete',
        onClick: () => handleDelete(row),
        disabled: row.userId === 1
      }, '删除')
    ]
  }
])

// 分页配置
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0
})
</script>
```

### 3. API 接口转换

#### HTTP 请求方式转换
```javascript
// RuoYi 原始 API (JavaScript)
import request from '@/utils/request'

export function listUser(query) {
  return request({
    url: '/system/user/list',
    method: 'get',
    params: query
  })
}

export function getUser(userId) {
  return request({
    url: '/system/user/' + userId,
    method: 'get'
  })
}

export function addUser(data) {
  return request({
    url: '/system/user',
    method: 'post',
    data: data
  })
}
```

```typescript
// art-design-pro 目标 API (TypeScript)
import { http } from '@/utils/http'
import type { Api } from '@/types'

export class UserApi {
  /**
   * 查询用户列表
   */
  static async getUserList(params: {
    pageNum?: number
    pageSize?: number
    userName?: string
    phonenumber?: string
    status?: string
    beginTime?: string
    endTime?: string
  }): Promise<{
    rows: Api.System.User[]
    total: number
  }> {
    return http.get('/system/user/list', { params })
  }

  /**
   * 查询用户详细信息
   */
  static async getUser(userId: number): Promise<Api.System.User> {
    return http.get(`/system/user/${userId}`)
  }

  /**
   * 新增用户
   */
  static async addUser(data: Partial<Api.System.User>): Promise<void> {
    return http.post('/system/user', data)
  }

  /**
   * 修改用户
   */
  static async updateUser(data: Partial<Api.System.User>): Promise<void> {
    return http.put('/system/user', data)
  }

  /**
   * 删除用户
   */
  static async deleteUser(userIds: number[]): Promise<void> {
    return http.delete(`/system/user/${userIds.join(',')}`)
  }

  /**
   * 重置用户密码
   */
  static async resetPassword(userId: number, password: string): Promise<void> {
    return http.put('/system/user/resetPwd', { userId, password })
  }

  /**
   * 修改用户状态
   */
  static async changeStatus(userId: number, status: string): Promise<void> {
    return http.put('/system/user/changeStatus', { userId, status })
  }
}
```

### 4. 权限控制转换

#### 指令权限转换
```vue
<!-- RuoYi 原始权限控制 -->
<el-button v-hasPermi="['system:user:add']" @click="handleAdd">新增</el-button>
<el-button v-hasPermi="['system:user:edit']" @click="handleUpdate">修改</el-button>
<el-button v-hasPermi="['system:user:remove']" @click="handleDelete">删除</el-button>

<!-- art-design-pro 目标权限控制 -->
<el-button v-auth="'add'" @click="handleAdd">新增</el-button>
<el-button v-auth="'edit'" @click="handleUpdate">修改</el-button>
<el-button v-auth="'remove'" @click="handleDelete">删除</el-button>

<!-- 或使用方法权限 -->
<el-button v-if="hasAuth('add')" @click="handleAdd">新增</el-button>
<el-button v-if="hasAuth('edit')" @click="handleUpdate">修改</el-button>
<el-button v-if="hasAuth('remove')" @click="handleDelete">删除</el-button>
```

#### 权限配置转换
```typescript
// 路由权限配置
{
  path: '/system/user',
  name: 'SystemUser',
  component: () => import('@/views/system/user/index.vue'),
  meta: {
    title: '用户管理',
    icon: 'user',
    authList: [
      { title: '新增', authMark: 'add' },
      { title: '编辑', authMark: 'edit' },
      { title: '删除', authMark: 'remove' },
      { title: '重置密码', authMark: 'resetPwd' },
      { title: '分配角色', authMark: 'authRole' }
    ]
  }
}
```

## 测试策略

### 1. 单元测试
```typescript
// 用户管理组件测试
import { mount } from '@vue/test-utils'
import UserManagement from '@/views/system/user/index.vue'

describe('UserManagement', () => {
  test('should render user list', async () => {
    const wrapper = mount(UserManagement)
    expect(wrapper.find('.art-table').exists()).toBe(true)
  })

  test('should handle user add', async () => {
    const wrapper = mount(UserManagement)
    await wrapper.find('[data-test="add-user"]').trigger('click')
    expect(wrapper.vm.dialogVisible).toBe(true)
  })
})
```

### 2. 集成测试
```typescript
// API 集成测试
import { UserApi } from '@/api/system/user'

describe('UserApi', () => {
  test('should get user list', async () => {
    const result = await UserApi.getUserList({ pageNum: 1, pageSize: 10 })
    expect(result.rows).toBeInstanceOf(Array)
    expect(typeof result.total).toBe('number')
  })

  test('should create user', async () => {
    const userData = {
      userName: 'testuser',
      nickName: '测试用户',
      email: '<EMAIL>'
    }
    await expect(UserApi.addUser(userData)).resolves.not.toThrow()
  })
})
```

### 3. E2E 测试
```typescript
// 端到端测试
import { test, expect } from '@playwright/test'

test('user management workflow', async ({ page }) => {
  await page.goto('/system/user')
  
  // 测试用户列表加载
  await expect(page.locator('.art-table')).toBeVisible()
  
  // 测试新增用户
  await page.click('[data-test="add-user"]')
  await page.fill('[data-test="userName"]', 'testuser')
  await page.fill('[data-test="nickName"]', '测试用户')
  await page.click('[data-test="submit"]')
  
  // 验证用户添加成功
  await expect(page.locator('text=testuser')).toBeVisible()
})
```

## 部署配置

### 1. 环境变量配置
```bash
# .env.development
VITE_API_BASE_URL=http://localhost:8080
VITE_APP_TITLE=系统管理平台
VITE_APP_MODE=development

# .env.production
VITE_API_BASE_URL=https://api.example.com
VITE_APP_TITLE=系统管理平台
VITE_APP_MODE=production
```

### 2. 构建配置
```typescript
// vite.config.ts
export default defineConfig({
  build: {
    rollupOptions: {
      output: {
        manualChunks: {
          'system-management': [
            './src/views/system/user/index.vue',
            './src/views/system/role/index.vue',
            './src/views/system/menu/index.vue',
            './src/views/system/dept/index.vue'
          ]
        }
      }
    }
  }
})
```

---

**文档版本**: 1.0  
**创建日期**: 2024年12月  
**更新日期**: 2024年12月
