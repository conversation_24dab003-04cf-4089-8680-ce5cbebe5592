/**
 * 部门管理相关类型定义
 */

import type { RuoyiQueryParams } from '@/types/http'

/** 部门信息 */
export interface Dept {
  /** 部门id */
  deptId?: number
  /** 父部门id */
  parentId?: number
  /** 祖级列表 */
  ancestors?: string
  /** 部门名称 */
  deptName: string
  /** 显示顺序 */
  orderNum?: number
  /** 负责人 */
  leader?: string
  /** 联系电话 */
  phone?: string
  /** 邮箱 */
  email?: string
  /** 部门状态（0正常 1停用） */
  status?: string
  /** 删除标志（0代表存在 2代表删除） */
  delFlag?: string
  /** 创建者 */
  createBy?: string
  /** 创建时间 */
  createTime?: string
  /** 更新者 */
  updateBy?: string
  /** 更新时间 */
  updateTime?: string
  /** 子部门 */
  children?: Dept[]
  /** 树形结构标签 */
  label?: string
  /** 树形结构ID */
  id?: number
}

/** 部门查询参数 */
export interface DeptQueryParams extends RuoyiQueryParams {
  /** 部门名称 */
  deptName?: string
  /** 部门状态 */
  status?: string
}
