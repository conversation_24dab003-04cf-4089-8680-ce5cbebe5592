import type { Router } from 'vue-router'
import { setupBeforeEachGuard } from './guards/beforeEach'
import { setupAfterEachGuard } from './guards/afterEach'

/**
 * 路由守卫系统
 * 基于RuoYi路由守卫模式，直接处理RuoYi权限数据
 * 实现登录验证、权限验证、动态路由生成等功能
 */

/**
 * 设置路由守卫
 * @param router 路由实例
 */
export function setupRouterGuard(router: Router): void {
  // 设置前置守卫
  setupBeforeEachGuard(router)
  // 设置后置守卫
  setupAfterEachGuard(router)
}

/**
 * 路由守卫功能说明：
 *
 * 1. 白名单检查：
 *    - 登录页、注册页、忘记密码页等无需登录即可访问
 *    - 异常页面（403、404、500）可直接访问
 *
 * 2. 登录状态检查：
 *    - 检查用户是否已登录（token存在且有效）
 *    - 未登录用户重定向到登录页，并携带原始路径作为重定向参数
 *
 * 3. 用户信息验证：
 *    - 检查用户信息是否完整（用户信息、角色、权限）
 *    - 如果信息不完整，自动获取用户信息和权限
 *
 * 4. 动态路由生成：
 *    - 根据用户角色和权限生成可访问的路由
 *    - 支持前端权限模式和后端权限模式
 *    - 自动注册动态路由到路由实例
 *
 * 5. 权限验证：
 *    - 基于RuoYi权限标识（如'system:user:add'）进行权限验证
 *    - 支持角色权限和功能权限的双重验证
 *
 * 6. 错误处理：
 *    - 统一的错误处理机制
 *    - 权限不足跳转到403页面
 *    - 系统错误跳转到500页面
 *
 * 7. 用户体验优化：
 *    - 进度条显示
 *    - Loading状态管理
 *    - 页面标题设置
 *    - 工作台状态管理
 */
