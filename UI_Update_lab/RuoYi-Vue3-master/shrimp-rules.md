# RuoYi Vue3 项目开发规范

## 项目概述

- **技术栈**: Vue3 + Vite + Element Plus + Pinia + Vue Router
- **项目类型**: 后台管理系统前端
- **构建工具**: Vite 6.3.5
- **UI 框架**: Element Plus
- **状态管理**: Pinia
- **开发端口**: 80

## 项目架构规范

### 目录结构约定

- **src/api/**: API 接口定义，按业务模块分类（system/, monitor/, tool/）
- **src/components/**: 可复用组件，每个组件独立文件夹
- **src/views/**: 页面组件，按功能模块分类
- **src/store/**: Pinia 状态管理，模块化存储
- **src/router/**: 路由配置文件
- **src/utils/**: 工具函数和公共方法
- **src/assets/**: 静态资源（图片、样式、图标）
- **src/directive/**: 自定义指令
- **src/plugins/**: 插件配置
- **src/layout/**: 布局组件

### 路径别名配置

- **必须使用 `@` 别名**: 引用 src 目录下的文件
- **必须使用 `~` 别名**: 引用 node_modules 目录
- **禁止使用相对路径**: 跨目录引用时禁止使用 `../` 形式

## 代码标准

### 文件命名约定

- **组件文件**: 使用 PascalCase（如 `UserList.vue`）
- **普通文件**: 使用 kebab-case（如 `user-api.js`）
- **目录名**: 使用 kebab-case 或 camelCase
- **Vue 文件**: 必须使用 `.vue` 扩展名

### 组件命名规范

- **组件名**: 必须使用 PascalCase
- **组件注册**: 全局组件在 `main.js` 注册，局部组件按需导入
- **组件 props**: 使用 camelCase
- **事件名**: 使用 kebab-case

### 导入规范

```javascript
// 正确：使用别名
import { getUserList } from '@/api/system/user'
import UserForm from '@/components/UserForm/index.vue'

// 错误：使用相对路径
import { getUserList } from '../../../api/system/user'
```

## 功能实现标准

### API 接口规范

- **新增 API**: 必须在 `src/api/` 对应模块目录下创建
- **接口文件**: 按业务功能分组，一个文件包含相关接口
- **请求方法**: 使用 `src/utils/request.js` 封装的 axios 实例
- **接口命名**: 使用动词+名词形式（如 `getUserList`, `addUser`）

### 页面组件规范

- **新增页面**: 必须在 `src/views/` 对应模块目录下创建
- **页面结构**: 使用 `<template>`, `<script setup>`, `<style scoped>` 结构
- **路由配置**: 新页面必须在 `src/router/index.js` 配置路由
- **权限控制**: 使用 `v-hasPermi` 指令或路由 meta 配置权限

### 组件开发规范

- **可复用组件**: 放在 `src/components/` 目录
- **组件结构**: 每个组件独立文件夹，包含 `index.vue` 主文件
- **全局组件**: 在 `src/main.js` 中注册
- **组件通信**: 优先使用 props/emit，复杂状态使用 Pinia

### 状态管理规范

- **Store 模块**: 按功能模块划分，放在 `src/store/modules/`
- **状态命名**: 使用 camelCase
- **Action 命名**: 使用动词形式
- **持久化**: 使用 `src/utils/auth.js` 处理 token 存储

## 样式规范

### CSS 类命名

- **使用 BEM 命名法**: `.block__element--modifier`
- **作用域样式**: 组件样式必须使用 `<style scoped>`
- **全局样式**: 放在 `src/assets/styles/` 目录

### Element Plus 使用

- **主题定制**: 使用 CSS 变量覆盖默认主题
- **组件引用**: 按需导入，避免全量引入
- **图标使用**: 优先使用 Element Plus 图标

## 关键文件交互标准

### 多文件协调要求

- **添加新页面时**:
  1. 在 `src/views/` 创建页面组件
  2. 在 `src/router/index.js` 配置路由
  3. 如需权限控制，在路由 meta 中配置
  4. 如需 API，在 `src/api/` 对应模块创建接口

- **添加新组件时**:
  1. 在 `src/components/` 创建组件文件夹
  2. 如需全局使用，在 `src/main.js` 注册
  3. 如需样式，使用 scoped 样式

- **修改配置时**:
  1. 环境变量修改需同时更新 `.env.development`, `.env.production`
  2. 构建配置修改需更新 `vite.config.js`
  3. 依赖更新需同时更新 `package.json`

### 权限系统交互

- **页面权限**: 在路由 meta 中配置 `roles` 或 `permissions`
- **按钮权限**: 使用 `v-hasPermi` 指令
- **菜单权限**: 通过后端接口动态生成

## AI 决策标准

### 优先级判断

1. **高优先级**: 安全性、数据一致性、用户体验
2. **中优先级**: 代码复用性、性能优化
3. **低优先级**: 代码美观、注释完整性

### 技术选择决策树

- **新增功能**: 优先使用现有组件和工具函数
- **状态管理**: 简单状态用 ref/reactive，复杂状态用 Pinia
- **样式实现**: 优先使用 Element Plus 组件，自定义样式使用 scoped
- **API 调用**: 必须使用 `src/utils/request.js` 封装的方法

### 错误处理标准

- **API 错误**: 使用全局错误处理，特殊情况可局部处理
- **表单验证**: 使用 Element Plus 表单验证规则
- **路由错误**: 配置 404 和权限错误页面

## 禁止操作清单

### 严格禁止

- **禁止直接修改 node_modules**
- **禁止在组件中直接操作 DOM**
- **禁止使用内联样式**（除特殊动态样式）
- **禁止在 template 中写复杂逻辑**
- **禁止跳过 ESLint 检查**
- **禁止硬编码 API 地址**
- **禁止在生产环境使用 console.log**

### 代码质量禁止

- **禁止创建超过 300 行的组件文件**
- **禁止创建超过 50 行的函数**
- **禁止使用 any 类型**（如果使用 TypeScript）
- **禁止未处理的 Promise 异常**
- **禁止循环依赖**

### 安全性禁止

- **禁止在前端存储敏感信息**
- **禁止跳过权限验证**
- **禁止使用 eval() 函数**
- **禁止直接拼接 SQL 或 HTML**

## 特殊注意事项

### Element Plus 特定规范

- **表格组件**: 使用 `el-table`，必须设置 `row-key`
- **表单组件**: 使用 `el-form`，必须设置验证规则
- **对话框**: 使用 `el-dialog`，必须控制显示状态
- **消息提示**: 使用 `ElMessage` 而非 `alert`

### Vite 特定规范

- **环境变量**: 必须以 `VITE_` 前缀命名
- **静态资源**: 放在 `public/` 目录或使用 `import` 导入
- **动态导入**: 使用 `import()` 实现路由懒加载

### Vue3 特定规范

- **组合式 API**: 优先使用 `<script setup>` 语法
- **响应式**: 使用 `ref` 和 `reactive`，避免 `Vue.observable`
- **生命周期**: 使用组合式 API 生命周期钩子
- **插槽**: 使用 `v-slot` 语法，避免废弃的 `slot` 语法

---

**文档版本**: 1.0  
**最后更新**: 2024年12月  
**适用范围**: RuoYi Vue3 项目 AI Agent 操作指南