import { http } from '@/utils/http'
import { LoginApi } from '@/api/auth/login'

export class UserService {
  // 登录 - 使用RuoYi格式
  static login(params: Api.Auth.LoginParams) {
    return LoginApi.login({
      username: params.userName,
      password: params.password,
      code: params.code,
      uuid: params.uuid
    })
  }

  // 获取用户信息 - 使用RuoYi格式
  static getUserInfo() {
    return LoginApi.getUserInfo()
  }

  // 获取用户列表
  static getUserList(params: Api.Common.PaginatingSearchParams) {
    return http.get<Api.User.UserListData>('/api/user/list', { params })
  }
}
