import { staticRoutes } from '@/router/routes/staticRoutes'
import { menuDataToRouter } from '@/router/utils/menuToRouter'
import { AppRouteRecord } from '@/types/router'
import { http } from '@/utils/http'

interface MenuResponse {
  menuList: AppRouteRecord[]
}

interface RuoyiMenuResponse {
  code: number
  msg: string
  data: any[]
}

/**
 * 获取RuoYi后端路由数据
 * 与RuoYi原版getRouters接口保持一致
 */
export const getRouters = () => {
  return http({
    url: '/getRouters',
    method: 'get'
  })
}

/**
 * 将RuoYi格式的路由数据转换为art-design-pro格式
 */
function convertRuoyiToAppRoutes(ruoyiRoutes: any[]): AppRouteRecord[] {
  console.log('🔄 开始转换RuoYi路由格式')

  return ruoyiRoutes.map((route) => {
    console.log(`📝 转换路由: ${route.name} (${route.path})`)

    const converted: AppRouteRecord = {
      name: route.name,
      path: route.path,
      component: convertRuoyiComponent(route.component),
      meta: {
        title: route.meta?.title || route.name,
        icon: route.meta?.icon,
        hidden: route.hidden || false,
        keepAlive: !route.meta?.noCache,
        isIframe: !!route.meta?.link,
        link: route.meta?.link
      },
      children: route.children ? convertRuoyiToAppRoutes(route.children) : []
    }

    console.log(`✅ 转换完成: ${String(converted.name)}`)
    return converted
  })
}

/**
 * 转换RuoYi组件路径为art-design-pro格式
 */
function convertRuoyiComponent(component: string): any {
  if (!component) return '/index/index'

  // RuoYi特殊组件映射，返回别名路径字符串，方便后续统一处理
  const componentMap: Record<string, string> = {
    Layout: '/index/index',
    ParentView: '/index/index',
    InnerLink: '/index/index'
  }

  if (componentMap[component]) {
    return componentMap[component]
  }

  // 动态组件路径转换（返回规范化的相对views目录路径，供 import.meta.glob 映射）
  // 示例：RuoYi: system/user/index -> 这里返回: /system/user/index
  if (component.includes('/')) {
    return component.startsWith('/') ? component : `/${component}`
  }

  // 兜底：按目录/index 规则
  return `/${component}/index`
}

/**
 * 从静态路由中提取演示路由
 * 提取需要在菜单中显示的演示路由
 */
function extractDemoRoutesFromStatic(): AppRouteRecord[] {
  // 提取演示路由（排除登录、注册、异常页面等）
  const demoRouteNames = ['Dashboard', 'Template', 'Widgets', 'Examples']

  console.log('🔍 开始提取演示路由')
  console.log('📋 静态路由总数:', staticRoutes.length)
  console.log('🎯 目标演示路由名称:', demoRouteNames)

  // 打印所有静态路由的名称用于调试
  const allRouteNames = staticRoutes.map((route) => route.name).filter(Boolean)
  console.log('📝 所有静态路由名称:', allRouteNames)

  const filteredRoutes = staticRoutes.filter((route) => {
    const isDemo = demoRouteNames.includes(route.name as string)
    if (isDemo) {
      console.log(`✅ 匹配到演示路由: ${String(route.name)} (${route.path})`)
    }
    return isDemo
  })

  console.log('🎨 过滤后的演示路由数量:', filteredRoutes.length)

  const result = filteredRoutes.map((route) => ({
    name: route.name,
    path: route.path,
    component: route.component,
    meta: route.meta || {},
    children:
      route.children?.map((child) => ({
        name: child.name,
        path: child.path,
        component: child.component,
        meta: child.meta || {}
      })) || []
  })) as AppRouteRecord[]

  console.log(
    '🚀 最终提取的演示路由:',
    result.map((r) => `${String(r.name)} (${r.path})`)
  )
  return result
}

// 菜单接口
export const menuService = {
  /**
   * 获取菜单列表（支持静态演示路由和后端动态路由合并）
   * @param delay 模拟延迟时间
   * @param useBackendRoutes 是否使用后端路由
   */
  async getMenuList(delay = 300, useBackendRoutes = true): Promise<MenuResponse> {
    try {
      let menuList: AppRouteRecord[] = []

      // 1. 首先获取静态演示路由
      const demoRoutes = extractDemoRoutesFromStatic()
      console.log('提取到的演示路由数量:', demoRoutes.length)

      // 2. 处理演示路由
      const processedDemoRoutes = demoRoutes.map((route) => menuDataToRouter(route))

      if (useBackendRoutes) {
        try {
          // 3. 尝试从后端获取动态路由
          const axiosResponse = await getRouters()
          // 从Axios响应中提取实际的RuoYi数据
          const ruoyiData = (axiosResponse as any).data as RuoyiMenuResponse

          console.log('🔍 Axios响应结构:', axiosResponse)
          console.log('🔍 RuoYi数据:', ruoyiData)
          console.log('🔍 RuoYi code:', ruoyiData?.code)
          console.log('🔍 RuoYi data:', ruoyiData?.data)
          console.log('🔍 路由数组长度:', ruoyiData?.data?.length)

          if (ruoyiData.code === 200 && ruoyiData.data && ruoyiData.data.length > 0) {
            console.log('✅ 获取到后端动态路由数据，数量:', ruoyiData.data.length)
            // 转换RuoYi格式的路由数据为art-design-pro格式
            const backendRoutes = convertRuoyiToAppRoutes(ruoyiData.data)
            const processedBackendRoutes = backendRoutes.map((route) => menuDataToRouter(route))

            // 合并演示路由和后端路由
            menuList = [...processedDemoRoutes, ...processedBackendRoutes]
            console.log('🎯 合并后的菜单总数:', menuList.length)
          } else {
            console.log('后端路由数据为空，仅使用演示路由')
            menuList = processedDemoRoutes
          }
        } catch (error) {
          console.warn('后端路由接口调用失败，仅使用演示路由:', error)
          menuList = processedDemoRoutes
        }
      } else {
        // 仅使用演示路由
        console.log('仅使用演示路由配置')
        menuList = processedDemoRoutes
      }

      // 模拟接口延迟
      await new Promise((resolve) => setTimeout(resolve, delay))

      console.log('最终菜单列表数量:', menuList.length)
      return { menuList }
    } catch (error) {
      throw error instanceof Error ? error : new Error('获取菜单失败')
    }
  }
}
