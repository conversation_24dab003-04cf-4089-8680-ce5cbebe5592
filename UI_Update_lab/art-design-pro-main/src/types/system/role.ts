/**
 * 角色管理相关类型定义
 */

import type { RuoyiQueryParams } from '@/types/http'

/** 角色信息 */
export interface Role {
  /** 角色ID */
  roleId?: number
  /** 角色名称 */
  roleName: string
  /** 角色权限字符串 */
  roleKey: string
  /** 显示顺序 */
  roleSort?: number
  /** 数据范围（1：全部数据权限 2：自定数据权限 3：本部门数据权限 4：本部门及以下数据权限） */
  dataScope?: string
  /** 菜单树选择项是否关联显示 */
  menuCheckStrictly?: boolean
  /** 部门树选择项是否关联显示 */
  deptCheckStrictly?: boolean
  /** 角色状态（0正常 1停用） */
  status?: string
  /** 删除标志（0代表存在 2代表删除） */
  delFlag?: string
  /** 创建者 */
  createBy?: string
  /** 创建时间 */
  createTime?: string
  /** 更新者 */
  updateBy?: string
  /** 更新时间 */
  updateTime?: string
  /** 备注 */
  remark?: string
  /** 用户是否存在此角色标识 默认不存在 */
  flag?: boolean
  /** 菜单组 */
  menuIds?: number[]
  /** 部门组（数据权限） */
  deptIds?: number[]
}

/** 角色查询参数 */
export interface RoleQueryParams extends RuoyiQueryParams {
  /** 角色名称 */
  roleName?: string
  /** 权限字符 */
  roleKey?: string
  /** 角色状态 */
  status?: string
  /** 开始时间 */
  beginTime?: string
  /** 结束时间 */
  endTime?: string
}
