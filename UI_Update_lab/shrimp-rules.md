# UI_Update_lab 多项目工作空间开发规范

## 项目概述

- **工作空间类型**: 多项目 Vue3 前端开发实验室
- **子项目**: RuoYi-Vue3-master (后台管理系统) + art-design-pro-main (现代化管理平台)
- **共同技术栈**: Vue3 + Vite + Element Plus + Pinia + Vue Router
- **差异化技术**: RuoYi (JavaScript) vs art-design-pro (TypeScript + 高级工具链)

## 工作空间架构规范

### 项目识别规则

- **RuoYi-Vue3-master/**: 传统后台管理系统，JavaScript 技术栈
- **art-design-pro-main/**: 现代化管理平台，TypeScript + 完整工具链
- **根目录**: 仅存放工作空间级别的配置和文档

### 子项目特征识别

**RuoYi-Vue3 特征**:
- 使用 JavaScript
- 端口 80
- 简化的目录结构
- 基础的 ESLint 配置

**art-design-pro 特征**:
- 使用 TypeScript
- 完整的 lint-staged + husky 工具链
- 复杂的目录结构 (composables/, enums/, types/)
- 国际化支持 (locales/)

## 多项目协调标准

### 项目选择决策树

1. **新功能开发**:
   - 简单 CRUD 操作 → RuoYi-Vue3
   - 复杂业务逻辑 → art-design-pro
   - 需要 TypeScript → art-design-pro
   - 快速原型 → RuoYi-Vue3

2. **组件复用**:
   - 基础组件可在两项目间复制
   - 必须适配目标项目的技术栈
   - TypeScript 组件需转换为 JavaScript

### 跨项目文件操作规则

- **禁止跨项目直接引用**: 两个子项目完全独立
- **组件迁移**: 必须完整复制并适配目标项目
- **配置同步**: 相似配置需手动同步，不可共享文件
- **依赖管理**: 每个项目独立管理 package.json

## 代码标准

### 通用命名规范

- **组件文件**: PascalCase (UserList.vue)
- **普通文件**: kebab-case (user-api.js/ts)
- **目录名**: kebab-case 或 camelCase
- **路径别名**: 统一使用 `@` 指向 src 目录

### TypeScript 特定规范 (art-design-pro)

- **类型定义**: 放在 `src/types/` 目录
- **接口命名**: 使用 I 前缀 (IUserInfo)
- **枚举命名**: 使用 PascalCase
- **禁止使用 any**: 必须明确类型定义

### JavaScript 特定规范 (RuoYi-Vue3)

- **变量声明**: 优先使用 const/let
- **函数定义**: 优先使用箭头函数
- **对象解构**: 优先使用解构赋值

## 功能实现标准

### API 接口规范

**RuoYi-Vue3**:
- API 文件位置: `src/api/`
- 使用 `src/utils/request.js`
- 接口命名: 动词+名词 (getUserList)

**art-design-pro**:
- API 文件位置: `src/api/`
- 使用 TypeScript 类型定义
- 接口命名: 动词+名词 (getUserList)
- 必须定义返回类型

### 路由配置规范

**RuoYi-Vue3**:
- 路由文件: `src/router/index.js`
- 权限控制: 使用 meta.roles

**art-design-pro**:
- 路由文件: `src/router/index.ts`
- 路由类型: 使用 TypeScript 接口
- 权限控制: 使用指令和路由守卫

### 状态管理规范

**通用 Pinia 规范**:
- Store 模块化: 按功能划分
- 状态命名: camelCase
- Action 命名: 动词形式
- 持久化: 使用对应项目的存储工具

## 关键文件交互标准

### 新增页面协调流程

**RuoYi-Vue3 新增页面**:
1. 在 `RuoYi-Vue3-master/src/views/` 创建页面
2. 在 `RuoYi-Vue3-master/src/router/index.js` 配置路由
3. 如需 API，在 `RuoYi-Vue3-master/src/api/` 创建接口

**art-design-pro 新增页面**:
1. 在 `art-design-pro-main/src/views/` 创建页面
2. 在 `art-design-pro-main/src/router/index.ts` 配置路由
3. 定义 TypeScript 类型在 `src/types/`
4. 如需 API，在 `art-design-pro-main/src/api/` 创建接口

### 组件开发协调

- **全局组件**: 在各自项目的 main.js/ts 注册
- **局部组件**: 放在各自的 components 目录
- **样式隔离**: 必须使用 scoped 样式

## AI 决策标准

### 项目选择优先级

1. **明确指定项目**: 严格按用户指定执行
2. **功能复杂度**: 复杂功能选择 art-design-pro
3. **开发速度**: 快速开发选择 RuoYi-Vue3
4. **类型安全**: 需要类型检查选择 art-design-pro

### 技术选择决策

- **新增依赖**: 检查项目现有技术栈兼容性
- **组件选择**: 优先使用项目现有组件库
- **工具使用**: 遵循项目现有工具链配置

### 错误处理策略

- **构建错误**: 检查对应项目的配置文件
- **类型错误**: 仅在 TypeScript 项目中处理
- **依赖冲突**: 在对应项目目录执行包管理命令

## 禁止操作清单

### 跨项目禁止操作

- **禁止跨项目文件引用**
- **禁止共享 node_modules**
- **禁止在根目录安装项目依赖**
- **禁止混合两个项目的配置**

### 技术栈混用禁止

- **禁止在 RuoYi 中使用 TypeScript 语法**
- **禁止在 art-design-pro 中降级为 JavaScript**
- **禁止忽略项目特定的 lint 规则**
- **禁止跳过项目的构建流程**

### 文件操作禁止

- **禁止修改子项目的 package.json 从根目录**
- **禁止在错误的项目目录执行命令**
- **禁止删除项目特定的配置文件**
- **禁止合并两个项目的构建产物**

## 特殊注意事项

### 开发环境隔离

- **端口分配**: RuoYi (80), art-design-pro (默认 Vite 端口)
- **构建产物**: 各自项目独立的 dist 目录
- **开发服务**: 必须在对应项目目录启动

### 版本控制协调

- **提交范围**: 明确标识修改的子项目
- **分支策略**: 可按子项目创建功能分支
- **合并策略**: 避免跨项目的无关修改

### 部署协调

- **独立部署**: 两个项目可独立部署
- **资源隔离**: 静态资源不可混用
- **环境变量**: 各自项目独立配置

---

**文档版本**: 1.0  
**最后更新**: 2024年12月  
**适用范围**: UI_Update_lab 工作空间 AI Agent 操作指南
