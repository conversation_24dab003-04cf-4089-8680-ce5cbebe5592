<template>
  <div class="login">
    <LoginLeftView></LoginLeftView>

    <div class="right-wrap">
      <div class="top-right-wrap">
        <div class="btn theme-btn" @click="themeAnimation">
          <i class="iconfont-sys">
            {{ isDark ? '&#xe6b5;' : '&#xe725;' }}
          </i>
        </div>
        <ElDropdown @command="changeLanguage" popper-class="langDropDownStyle">
          <div class="btn language-btn">
            <i class="iconfont-sys icon-language">&#xe611;</i>
          </div>
          <template #dropdown>
            <ElDropdownMenu>
              <div v-for="lang in languageOptions" :key="lang.value" class="lang-btn-item">
                <ElDropdownItem
                  :command="lang.value"
                  :class="{ 'is-selected': locale === lang.value }"
                >
                  <span class="menu-txt">{{ lang.label }}</span>
                  <i v-if="locale === lang.value" class="iconfont-sys icon-check">&#xe621;</i>
                </ElDropdownItem>
              </div>
            </ElDropdownMenu>
          </template>
        </ElDropdown>
      </div>
      <div class="header">
        <ArtLogo class="icon" />
        <h1>{{ systemName }}</h1>
      </div>
      <div class="login-wrap">
        <div class="form">
          <h3 class="title">{{ $t('login.title') }}</h3>
          <p class="sub-title">{{ $t('login.subTitle') }}</p>
          <ElForm
            ref="formRef"
            :model="loginForm"
            :rules="loginRules"
            @keyup.enter="handleLogin"
            style="margin-top: 25px"
          >
            <ElFormItem prop="username">
              <ElInput
                v-model="loginForm.username"
                placeholder="账号"
                size="large"
                auto-complete="off"
              >
                <template #prefix>
                  <ElIcon><User /></ElIcon>
                </template>
              </ElInput>
            </ElFormItem>
            <ElFormItem prop="password">
              <ElInput
                v-model="loginForm.password"
                type="password"
                placeholder="密码"
                size="large"
                auto-complete="off"
                show-password
                @keyup.enter="handleLogin"
              >
                <template #prefix>
                  <ElIcon><Lock /></ElIcon>
                </template>
              </ElInput>
            </ElFormItem>
            <ElFormItem prop="code" v-if="captchaEnabled">
              <div style="display: flex; align-items: center; gap: 10px">
                <ElInput
                  v-model="loginForm.code"
                  placeholder="验证码"
                  size="large"
                  auto-complete="off"
                  style="flex: 1"
                  @keyup.enter="handleLogin"
                >
                  <template #prefix>
                    <ElIcon><Key /></ElIcon>
                  </template>
                </ElInput>
                <div class="captcha-image" @click="getCaptcha">
                  <img v-if="codeUrl" :src="codeUrl" alt="验证码" />
                  <div v-else class="captcha-placeholder">点击获取</div>
                </div>
              </div>
            </ElFormItem>

            <div class="forget-password">
              <ElCheckbox v-model="loginForm.rememberMe">记住密码</ElCheckbox>
              <RouterLink to="/auth/forget-password">忘记密码</RouterLink>
            </div>

            <div style="margin-top: 30px">
              <ElButton
                class="login-btn"
                type="primary"
                size="large"
                style="width: 100%"
                @click="handleLogin"
                :loading="loading"
              >
                <span v-if="!loading">登 录</span>
                <span v-else>登 录 中...</span>
              </ElButton>
            </div>

            <div class="footer" v-if="registerEnabled">
              <p>
                没有账号？
                <RouterLink to="/auth/register">立即注册</RouterLink>
              </p>
            </div>
          </ElForm>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive, onMounted, computed, watch } from 'vue'
  import { storeToRefs } from 'pinia'
  import { useRouter, useRoute } from 'vue-router'
  import { ElNotification, ElMessage, ElIcon } from 'element-plus'
  import { User, Lock, Key } from '@element-plus/icons-vue'
  import { useUserStore } from '@/store/modules/user'
  import { useSettingStore } from '@/store/modules/setting'
  import { LanguageEnum, SystemThemeEnum } from '@/enums/appEnum'
  import { useI18n } from 'vue-i18n'
  import { LoginApi } from '@/api/auth/login'
  import { encrypt, decrypt } from '@/utils/jsencrypt'
  import { setToken } from '@/utils/auth'
  import Cookies from 'js-cookie'
  import type { FormInstance, FormRules } from 'element-plus'

  defineOptions({ name: 'Login' })

  const { locale } = useI18n()
  const router = useRouter()
  const route = useRoute()
  const userStore = useUserStore()
  const settingStore = useSettingStore()
  const { isDark } = storeToRefs(settingStore)

  // 主题切换动画
  const themeAnimation = () => {
    // 简单的主题切换逻辑
    const newTheme = settingStore.isDark ? SystemThemeEnum.LIGHT : SystemThemeEnum.DARK
    settingStore.setGlopTheme(newTheme, newTheme)
  }

  // 系统配置
  const systemName = '管理系统'

  // 表单引用
  const formRef = ref<FormInstance>()

  // 登录表单数据
  const loginForm = reactive({
    username: 'admin',
    password: 'admin123',
    code: '',
    uuid: '',
    rememberMe: false
  })

  // 表单验证规则
  const loginRules = computed<FormRules>(() => ({
    username: [{ required: true, message: '请输入您的账号', trigger: 'blur' }],
    password: [{ required: true, message: '请输入您的密码', trigger: 'blur' }],
    code: [{ required: true, message: '请输入验证码', trigger: 'change' }]
  }))

  // 状态变量
  const loading = ref(false)
  const codeUrl = ref('')
  const captchaEnabled = ref(true)
  const registerEnabled = ref(false)
  const redirect = ref<string | undefined>(undefined)

  // 语言选项
  const languageOptions = [
    { value: LanguageEnum.ZH, label: '中文' },
    { value: LanguageEnum.EN, label: 'English' }
  ]

  // 监听路由变化，获取重定向参数
  watch(
    route,
    (newRoute) => {
      redirect.value = newRoute.query?.redirect as string
    },
    { immediate: true }
  )

  // 组件挂载时初始化
  onMounted(() => {
    getCaptcha()
    getCookie()
  })

  // 获取验证码
  const getCaptcha = async () => {
    try {
      const response = await LoginApi.getCaptchaImage()
      const data = response.data
      if (data) {
        captchaEnabled.value = data.captchaEnabled !== false
        if (captchaEnabled.value) {
          codeUrl.value = 'data:image/gif;base64,' + (data.img || '')
          loginForm.uuid = data.uuid || ''
        }
      }
    } catch (error) {
      console.error('获取验证码失败:', error)
    }
  }

  // 获取Cookie中保存的登录信息
  const getCookie = () => {
    const username = Cookies.get('username')
    const password = Cookies.get('password')
    const rememberMe = Cookies.get('rememberMe')

    if (username) {
      loginForm.username = username
    }
    if (password) {
      loginForm.password = decrypt(password)
    }
    if (rememberMe) {
      loginForm.rememberMe = Boolean(rememberMe)
    }
  }

  // 登录处理
  const handleLogin = async () => {
    if (!formRef.value) return

    try {
      // 表单验证
      const valid = await formRef.value.validate()
      if (!valid) return

      loading.value = true

      // 处理记住密码
      if (loginForm.rememberMe) {
        Cookies.set('username', loginForm.username, { expires: 30 })
        Cookies.set('password', encrypt(loginForm.password), { expires: 30 })
        Cookies.set('rememberMe', String(loginForm.rememberMe), { expires: 30 })
      } else {
        Cookies.remove('username')
        Cookies.remove('password')
        Cookies.remove('rememberMe')
      }

      // 调用登录API
      const response = await LoginApi.login(loginForm)
      const token = response.data?.token

      if (!token) {
        throw new Error('登录失败 - 未获取到token')
      }

      // 存储token
      setToken(token)
      userStore.setTokenValue(token)

      // 获取用户信息
      const userInfoResponse = await LoginApi.getUserInfo()
      const userInfoData = userInfoResponse.data

      // 设置用户信息
      if (userInfoData) {
        // 直接使用RuoYi格式的用户信息
        userStore.setUserInfo(userInfoData.user)
        userStore.setRoles(userInfoData.roles)
        userStore.setPermissions(userInfoData.permissions)
      }

      // 登录成功提示
      showLoginSuccessNotice()

      // 跳转到目标页面
      const query = route.query
      const otherQueryParams = Object.keys(query).reduce(
        (acc, cur) => {
          if (cur !== 'redirect') {
            acc[cur] = query[cur]
          }
          return acc
        },
        {} as Record<string, any>
      )

      router.push({ path: redirect.value || '/', query: otherQueryParams })
    } catch (error) {
      console.error('登录失败:', error)
      ElMessage.error('登录失败，请检查用户名和密码')

      // 重新获取验证码
      if (captchaEnabled.value) {
        getCaptcha()
      }
    } finally {
      loading.value = false
    }
  }

  // 登录成功提示
  const showLoginSuccessNotice = () => {
    setTimeout(() => {
      ElNotification({
        title: '登录成功',
        type: 'success',
        duration: 2500,
        zIndex: 10000,
        message: `欢迎使用 ${systemName}!`
      })
    }, 150)
  }

  // 切换语言
  const changeLanguage = (lang: LanguageEnum) => {
    if (locale.value === lang) return
    locale.value = lang
    userStore.setLanguage(lang)
  }
</script>

<style lang="scss" scoped>
  @use './index';

  .captcha-image {
    width: 120px;
    height: 40px;
    border: 1px solid var(--el-border-color);
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    .captcha-placeholder {
      color: var(--el-text-color-placeholder);
      font-size: 12px;
    }
  }

  .forget-password {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 20px;

    a {
      color: var(--el-color-primary);
      text-decoration: none;
      font-size: 14px;

      &:hover {
        text-decoration: underline;
      }
    }
  }

  .footer {
    text-align: center;
    margin-top: 20px;
    color: var(--el-text-color-regular);
    font-size: 14px;

    a {
      color: var(--el-color-primary);
      text-decoration: none;

      &:hover {
        text-decoration: underline;
      }
    }
  }
</style>
