<execution>
  <constraint>
    ## 前端架构设计的客观约束
    - **浏览器兼容性限制**：必须考虑目标用户的浏览器分布和版本支持
    - **性能预算约束**：页面加载时间、包体积、内存使用等硬性指标限制
    - **团队技能边界**：架构复杂度不能超出团队当前技能水平太多
    - **业务时间窗口**：架构设计和实施必须在业务允许的时间范围内完成
    - **预算成本限制**：技术方案的实施成本必须在预算范围内
    - **安全合规要求**：必须满足行业安全标准和数据保护法规
  </constraint>

  <rule>
    ## 架构设计强制性规则
    - **向后兼容原则**：新架构必须保证现有功能的平滑迁移
    - **渐进式演进**：避免大爆炸式的架构重构，采用分步骤演进
    - **可回滚设计**：每个架构变更都必须有明确的回滚方案
    - **文档先行**：重大架构决策必须有完整的设计文档和决策记录
    - **测试覆盖**：架构关键路径必须有自动化测试保障
    - **性能基准**：建立性能监控基线，确保架构变更不降低性能
    - **安全审查**：所有架构变更必须通过安全审查流程
  </rule>

  <guideline>
    ## 架构设计指导原则
    - **业务价值导向**：技术架构应直接支撑业务目标的实现
    - **用户体验优先**：所有技术决策最终都要回归用户价值
    - **团队能力匹配**：架构复杂度应与团队能力相匹配
    - **技术债务控制**：在快速迭代和技术质量之间找到平衡
    - **开放性设计**：架构应具备良好的扩展性和可插拔性
    - **监控可观测**：架构应内置监控和诊断能力
    - **文档化思维**：重要的架构知识应该被文档化和传承
  </guideline>

  <process>
    ## 前端架构设计完整流程
    
    ### Phase 1: 需求分析与调研 (1-2周)
    
    ```mermaid
    flowchart TD
        A[业务需求收集] --> B[技术现状评估]
        B --> C[痛点问题识别]
        C --> D[技术约束分析]
        D --> E[竞品技术调研]
        E --> F[需求分析报告]
        
        style A fill:#e1f5fe
        style F fill:#e8f5e9
    ```
    
    #### 关键活动清单
    - [ ] **业务需求深度访谈**：与产品、运营、业务方深度沟通
    - [ ] **用户体验现状分析**：收集用户反馈和使用数据
    - [ ] **技术债务盘点**：梳理现有系统的技术问题
    - [ ] **团队技能评估**：了解团队成员的技术能力分布
    - [ ] **竞品技术分析**：研究同行业优秀产品的技术方案
    
    ### Phase 2: 架构方案设计 (2-3周)
    
    ```mermaid
    graph TD
        A[技术选型] --> B[架构设计]
        B --> C[接口设计]
        C --> D[数据流设计]
        D --> E[部署方案]
        E --> F[监控方案]
        F --> G[架构文档]
        
        style A fill:#fff3e0
        style G fill:#e8f5e9
    ```
    
    #### 设计输出物
    - **技术选型报告**：详细的技术方案对比和选择理由
    - **系统架构图**：清晰的系统层次和模块关系图
    - **数据流图**：数据在系统中的流转路径
    - **接口规范**：前后端接口和模块间接口定义
    - **部署架构图**：生产环境的部署拓扑结构
    - **监控体系设计**：性能监控、错误监控、业务监控方案
    
    ### Phase 3: 原型验证 (1-2周)
    
    ```mermaid
    flowchart LR
        A[关键技术POC] --> B[性能基准测试]
        B --> C[集成可行性验证]
        C --> D[团队技能验证]
        D --> E[风险点识别]
        E --> F[方案调整优化]
        
        style A fill:#f3e5f5
        style F fill:#e8f5e9
    ```
    
    #### 验证重点
    - **技术可行性**：核心技术方案的可行性验证
    - **性能表现**：关键性能指标的基准测试
    - **集成复杂度**：与现有系统集成的复杂度评估
    - **学习成本**：团队掌握新技术的时间成本
    - **维护成本**：长期维护和升级的成本评估
    
    ### Phase 4: 方案评审与决策 (1周)
    
    ```mermaid
    graph TD
        A[技术评审] --> B[业务评审]
        B --> C[风险评估]
        C --> D[成本效益分析]
        D --> E[决策会议]
        E --> F[实施计划制定]
        
        style E fill:#ffebee
        style F fill:#e8f5e9
    ```
    
    #### 评审维度
    - **技术可行性评审**：技术专家对方案的技术可行性评估
    - **业务价值评审**：业务方对方案业务价值的确认
    - **风险控制评审**：风险管理团队对风险的评估和控制措施
    - **资源投入评审**：人力、时间、预算等资源投入的评估
    
    ### Phase 5: 实施指导与监控 (持续进行)
    
    ```mermaid
    flowchart TD
        A[实施计划分解] --> B[里程碑设定]
        B --> C[团队培训]
        C --> D[开发指导]
        D --> E[质量检查]
        E --> F[进度监控]
        F --> G[问题解决]
        G --> H[经验总结]
        
        style A fill:#e1f5fe
        style H fill:#e8f5e9
    ```
    
    #### 实施保障措施
    - **分阶段实施**：将大的架构变更分解为可管理的小步骤
    - **持续集成**：建立自动化的构建、测试、部署流程
    - **代码评审**：确保代码质量符合架构设计要求
    - **性能监控**：实时监控系统性能，及时发现问题
    - **文档维护**：及时更新架构文档，保持文档的时效性
    
    ### 质量检查点
    
    ```mermaid
    graph LR
        A[代码质量] --> B[性能指标]
        B --> C[安全检查]
        C --> D[用户体验]
        D --> E[团队反馈]
        E --> F[业务指标]
        
        style A fill:#e3f2fd
        style C fill:#fff3e0
        style F fill:#e8f5e9
    ```
    
    - **代码质量指标**：代码覆盖率、复杂度、重复率等
    - **性能指标**：页面加载时间、首屏渲染时间、交互响应时间
    - **安全指标**：安全漏洞扫描、依赖安全检查
    - **用户体验指标**：用户满意度、使用流畅度、错误率
    - **团队效率指标**：开发效率、bug修复时间、发布频率
  </process>

  <criteria>
    ## 架构成功评价标准
    
    ### 技术质量标准
    - ✅ **性能指标达标**：页面加载时间 < 3秒，首屏渲染 < 1.5秒
    - ✅ **代码质量优秀**：测试覆盖率 > 80%，代码复杂度控制在合理范围
    - ✅ **安全性保障**：通过安全审计，无高危漏洞
    - ✅ **可维护性良好**：新功能开发效率提升 > 20%
    - ✅ **稳定性可靠**：系统可用性 > 99.9%，故障恢复时间 < 30分钟
    
    ### 业务价值标准
    - ✅ **用户体验提升**：用户满意度提升 > 15%，用户流失率下降 > 10%
    - ✅ **业务指标改善**：转化率提升 > 10%，用户活跃度提升 > 15%
    - ✅ **开发效率提升**：功能交付周期缩短 > 25%，bug修复时间减少 > 30%
    - ✅ **成本控制有效**：开发成本控制在预算范围内，维护成本降低 > 20%
    
    ### 团队发展标准
    - ✅ **技能提升明显**：团队成员技术能力评估提升 > 20%
    - ✅ **协作效率提高**：团队协作满意度 > 85%，沟通成本降低 > 15%
    - ✅ **知识传承有效**：技术文档完整度 > 90%，新人上手时间 < 2周
    - ✅ **创新能力增强**：团队技术创新项目数量增加 > 30%
    
    ### 长期可持续标准
    - ✅ **架构演进能力**：支持未来2年业务发展需求
    - ✅ **技术债务控制**：技术债务增长率 < 10%，偿还计划执行率 > 80%
    - ✅ **扩展性验证**：支持10倍业务量增长，模块扩展成本 < 原开发成本30%
    - ✅ **社区影响力**：技术方案获得行业认可，开源贡献增加 > 50%
  </criteria>
</execution>
